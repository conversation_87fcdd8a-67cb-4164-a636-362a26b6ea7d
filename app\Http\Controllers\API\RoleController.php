<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Http\Requests\Role\StoreRoleRequest;
use App\Http\Requests\Role\UpdateRoleRequest;
use App\Http\Requests\Role\UpdateRolePermissionsRequest;
use App\Http\Resources\Role\RoleResource;
use App\Services\RoleService;
use App\Services\ActivityLogService;
use App\Traits\ApiResponseTrait;
use App\Exceptions\Role\RoleNotFoundException;
use App\Exceptions\Role\RoleValidationException;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Throwable;

class RoleController extends Controller
{
    use ApiResponseTrait;

    protected $roleService;

    public function __construct(RoleService $roleService)
    {
        $this->roleService = $roleService;

        // --- Áp dụng Middleware để kiểm tra quyền CHUNG ---
        // Cách này hiệu quả cho việc kiểm tra "User có quyền thực hiện hành động này nói chung không?"

        /* // Quyền xem (index, show, getPermissions)
        $this->middleware('permission:view_roles')->only(['index', 'show', 'getPermissions']);

        // Quyền tạo (store)
        $this->middleware('permission:create_roles')->only(['store']);

        // Quyền sửa (update)
        $this->middleware('permission:edit_roles')->only(['update']);

        // Quyền xóa (destroy)
        $this->middleware('permission:delete_roles')->only(['destroy']);
        */
    }

    public function index()
    {
        try {
            $filters = [
                'search' => request('search'),
                'is_custom' => request('is_custom'),
                'permission' => request('permission'),
                'created_from' => request('created_from'),
                'created_to' => request('created_to')
            ];

            $perPage = request('per_page', 10);

            $paginator = $this->roleService->getList($filters, $perPage);

            return $this->paginatedResponse(
                paginator: $paginator,
                resource: RoleResource::class,
                message: 'Lấy danh sách vai trò thành công'
            );

        } catch (Throwable $e) {
            Log::error('Error fetching roles list: ' . $e->getMessage());
            return $this->errorResponse('Lỗi khi lấy danh sách vai trò', 500);
        }
    }

    public function store(StoreRoleRequest $request)
    {
        try {
            $role = $this->roleService->create($request->validated());

            // Log activity
            ActivityLogService::logAssetCreated($role);

            // Xóa cache có chủ đích sau khi tạo thành công
            $this->clearRoleCache();

            return $this->createdResponse(
                data: new RoleResource($role),
                message: 'Tạo vai trò thành công'
            );

        } catch (RoleValidationException $e) {
            return $this->errorResponse($e->getMessage(), 422, $e->getErrors());
        } catch (Throwable $e) {
            Log::error('Error creating Role: ' . $e->getMessage());
            return $this->errorResponse('Lỗi khi tạo vai trò', 500);
        }
    }

    public function show(string $idOrName)
    {
        try {
            $role = $this->roleService->getById($idOrName);

            return $this->successResponse(
                data: new RoleResource($role),
                message: 'Lấy thông tin vai trò thành công'
            );

        } catch (RoleNotFoundException $e) {
            return $this->errorResponse($e->getMessage(), 404);
        } catch (Throwable $e) {
            Log::error('Error fetching Role: ' . $e->getMessage());
            return $this->errorResponse('Lỗi khi lấy thông tin vai trò', 500);
        }
    }

    public function update(UpdateRoleRequest $request, string $idOrName)
    {
        try {
            // Get old values before update
            $oldRole = $this->roleService->getById($idOrName);
            $oldValues = $oldRole->toArray();

            $role = $this->roleService->update($idOrName, $request->validated());
            $newValues = $request->validated();

            // Log activity
            ActivityLogService::logAssetUpdated($role, $oldValues, $newValues);

            // Xóa cache có chủ đích sau khi cập nhật thành công
            $this->clearRoleCache();

            return $this->updatedResponse(
                data: new RoleResource($role),
                message: 'Cập nhật vai trò thành công'
            );

        } catch (RoleNotFoundException $e) {
            return $this->errorResponse($e->getMessage(), 404);
        } catch (RoleValidationException $e) {
            return $this->errorResponse($e->getMessage(), 422, $e->getErrors());
        } catch (Throwable $e) {
            Log::error('Error updating Role: ' . $e->getMessage());
            return $this->errorResponse('Lỗi khi cập nhật vai trò', 500);
        }
    }

    public function destroy(string $idOrName)
    {
        try {
            // Get old values before deletion
            $role = $this->roleService->getById($idOrName);
            $oldValues = $role->toArray();

            $this->roleService->delete($idOrName);

            // Log activity
            ActivityLogService::logAssetDeleted($role, $oldValues);

            // Xóa cache có chủ đích sau khi xóa thành công
            $this->clearRoleCache();

            return $this->deletedResponse('Xóa vai trò thành công');

        } catch (RoleNotFoundException $e) {
            return $this->errorResponse($e->getMessage(), 404);
        } catch (RoleValidationException $e) {
            return $this->errorResponse($e->getMessage(), 422);
        } catch (Throwable $e) {
            Log::error('Error deleting Role: ' . $e->getMessage());
            return $this->errorResponse('Lỗi khi xóa vai trò', 500);
        }
    }

    /**
     * Get permissions for a specific role.
     *
     * @param  string  $idOrName
     * @return \Illuminate\Http\JsonResponse
     */
    public function getPermissions(string $idOrName)
    {
        try {
            $permissions = $this->roleService->getPermissions($idOrName);

            $permissionsData = $permissions->map(function ($permission) {
                return [
                    'id' => $permission->id,
                    'name' => $permission->name,
                    'vi_name' => $permission->vi_name ?? $permission->name,
                    'group' => $permission->group ?? 'general',
                ];
            });

            return $this->successResponse(
                data: $permissionsData,
                message: 'Lấy danh sách quyền cho vai trò thành công'
            );

        } catch (RoleNotFoundException $e) {
            return $this->errorResponse($e->getMessage(), 404);
        } catch (Throwable $e) {
            Log::error('Error fetching permissions for role ' . $idOrName . ': ' . $e->getMessage());
            return $this->errorResponse('Lỗi khi lấy danh sách quyền cho vai trò', 500);
        }
    }

    /**
     * Update the permissions for a specified role.
     *
     * @param  UpdateRolePermissionsRequest  $request
     * @param  string  $idOrName
     * @return \Illuminate\Http\JsonResponse
     */
    public function updatePermissions(UpdateRolePermissionsRequest $request, string $idOrName)
    {
        try {
            $role = $this->roleService->updatePermissions($idOrName, $request->validated()['permissions']);

            // Xóa cache có chủ đích sau khi cập nhật thành công
            $this->clearRoleCache();

            return $this->updatedResponse(
                data: new RoleResource($role),
                message: 'Cập nhật quyền cho vai trò thành công'
            );

        } catch (RoleNotFoundException $e) {
            return $this->errorResponse($e->getMessage(), 404);
        } catch (Throwable $e) {
            Log::error('Error updating role permissions for ' . $idOrName . ': ' . $e->getMessage());
            return $this->errorResponse('Lỗi khi cập nhật quyền cho vai trò', 500);
        }
    }

    /**
     * Xóa cache có chủ đích cho dữ liệu vai trò
     */
    protected function clearRoleCache(): void
    {
        $cacheTag = 'roles-list';

        // Kiểm tra xem cache driver có hỗ trợ tags không
        $driver = config('cache.default');
        $supportedDrivers = ['redis', 'memcached', 'array'];

        if (in_array($driver, $supportedDrivers)) {
            Cache::tags([$cacheTag])->flush();
        } else {
            // Fallback: tăng cache version để invalidate tất cả cache
            $versionKey = 'roles_version';
            $currentVersion = Cache::get($versionKey, 0);
            Cache::put($versionKey, $currentVersion + 1, 7200); // 2 hours
        }
    }
}
