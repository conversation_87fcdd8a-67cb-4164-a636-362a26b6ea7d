<?php

namespace App\Listeners;

use App\Events\ActivityLogged;
use App\Events\AssetUpdated;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class LogAssetUpdated implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * Handle the event.
     */
    public function handle(AssetUpdated $event): void
    {
        $modelClass = get_class($event->model);
        $modelName = $this->getModelDisplayName($modelClass);
        $assetName = $event->model->ten ?? $event->model->name ?? $event->model->id;
        
        $userName = $event->user ? $event->user->name : '<PERSON>ệ thống';
        $description = "Người dùng {$userName} đã cập nhật {$modelName}: {$assetName}";

        // Add details about what changed
        $changedFields = array_keys($event->newValues);
        if (!empty($changedFields)) {
            $fieldNames = $this->getFieldDisplayNames($changedFields);
            $description .= " (Thay đổi: " . implode(', ', $fieldNames) . ")";
        }

        event(new ActivityLogged(
            $event->user?->id,
            'update',
            $description,
            $modelClass,
            $event->model->id,
            $event->oldValues,
            $event->newValues,
            $event->ipAddress,
            $event->userAgent
        ));
    }

    /**
     * Get display name for model
     */
    private function getModelDisplayName(string $modelClass): string
    {
        $modelMap = [
            'App\Models\API\Taisan\Congdap' => 'Cống đập',
            'App\Models\API\Taisan\Trambom' => 'Trạm bơm',
            'App\Models\API\Taisan\Kenhmuong' => 'Kênh mương',
            'App\Models\User' => 'Người dùng',
            'Spatie\Permission\Models\Role' => 'Vai trò',
            'Spatie\Permission\Models\Permission' => 'Quyền',
        ];

        return $modelMap[$modelClass] ?? class_basename($modelClass);
    }

    /**
     * Get display names for fields
     */
    private function getFieldDisplayNames(array $fields): array
    {
        $fieldMap = [
            'ten' => 'Tên',
            'name' => 'Tên',
            'email' => 'Email',
            'quymo_ct' => 'Quy mô công trình',
            'loai_ct' => 'Loại công trình',
            'nam_xd' => 'Năm xây dựng',
            'nam_sd' => 'Năm sử dụng',
            'dt_dat' => 'Diện tích đất',
            'tinhtrang' => 'Tình trạng',
            'dv_quanly' => 'Đơn vị quản lý',
            'phuongthuc' => 'Phương thức',
            'chuthich' => 'Chú thích',
            'geom' => 'Vị trí địa lý',
        ];

        return array_map(function($field) use ($fieldMap) {
            return $fieldMap[$field] ?? $field;
        }, $fields);
    }
}
