<?php

namespace App\Listeners;

use App\Events\ActivityLogged;
use App\Events\UserLoggedIn;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use App\Models\ActivityLog;

class LogUserLogin implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * Handle the event.
     */
    public function handle(UserLoggedIn $event): void
    {
        $description = "Người dùng {$event->user->name} ({$event->user->email}) đã đăng nhập vào hệ thống";

        event(new ActivityLogged(
            $event->user->id,
            'login',
            $description,
            'App\Models\User',
            $event->user->id,
            null,
            null,
            $event->ipAddress,
            $event->userAgent
        ));
    }
}
