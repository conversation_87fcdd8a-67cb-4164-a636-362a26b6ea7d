<?php

namespace App\Listeners;

use App\Events\UserLoggedIn;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use App\Models\ActivityLog;

class LogUserLogin implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * Handle the event.
     */
    public function handle(UserLoggedIn $event): void
    {
        $description = "Người dùng {$event->user->name} ({$event->user->email}) đã đăng nhập vào hệ thống";

        ActivityLog::createLog([
            'user_id' => $event->user->id,
            'action' => 'login',
            'description' => $description,
            'model_type' => 'App\\Models\\User',
            'model_id' => $event->user->id,
            'ip_address' => $event->ipAddress,
            'user_agent' => $event->userAgent,
        ]);
    }
}
