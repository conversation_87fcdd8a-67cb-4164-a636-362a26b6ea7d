<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Carbon\Carbon;

class ActivityLog extends Model
{
    protected $fillable = [
        'user_id',
        'action',
        'description',
        'model_type',
        'model_id',
        'old_values',
        'new_values',
        'ip_address',
        'user_agent',
    ];

    protected $casts = [
        'old_values' => 'array',
        'new_values' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Enhanced method to create activity log with validation and error handling
     */
    public static function createLog(array $data): ?self
    {
        try {
            // Validate required fields
            $validator = Validator::make($data, [
                'user_id' => 'required|integer|exists:users,id',
                'action' => 'required|string|max:100',
                'description' => 'required|string',
                'ip_address' => 'nullable|ip',
                'user_agent' => 'nullable|string|max:500',
                'model_type' => 'nullable|string|max:255',
                'model_id' => 'nullable|string|max:255',
                'old_values' => 'nullable|array',
                'new_values' => 'nullable|array',
            ]);

            if ($validator->fails()) {
                Log::warning('ActivityLog validation failed', [
                    'errors' => $validator->errors()->toArray(),
                    'data' => $data
                ]);
                return null;
            }

            // Create the log entry
            return self::create($validator->validated());

        } catch (\Exception $e) {
            Log::error('Failed to create activity log', [
                'error' => $e->getMessage(),
                'data' => $data
            ]);
            return null;
        }
    }

    /**
     * Relationship với User
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Scope để lọc theo action
     */
    public function scopeByAction($query, $action)
    {
        return $query->where('action', $action);
    }

    /**
     * Scope để lọc theo user
     */
    public function scopeByUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Scope để lọc theo khoảng thời gian
     */
    public function scopeByDateRange($query, $from, $to)
    {
        return $query->whereBetween('created_at', [$from, $to]);
    }

    /**
     * Scope để tìm kiếm trong description
     */
    public function scopeSearch($query, $search)
    {
        return $query->where('description', 'like', '%' . $search . '%');
    }

    /**
     * Scope để lọc theo model type
     */
    public function scopeByModelType($query, $modelType)
    {
        return $query->where('model_type', $modelType);
    }

    /**
     * Scope để lọc theo IP address
     */
    public function scopeByIpAddress($query, $ipAddress)
    {
        return $query->where('ip_address', $ipAddress);
    }

    /**
     * Scope để lấy logs trong N ngày gần đây
     */
    public function scopeRecent($query, $days = 30)
    {
        return $query->where('created_at', '>=', now()->subDays($days));
    }

    /**
     * Scope để lấy logs theo loại hành động cụ thể
     */
    public function scopeByActionType($query, $actionType)
    {
        $actionMap = [
            'auth' => ['login', 'logout'],
            'crud' => ['create', 'update', 'delete'],
            'view' => ['view', 'view_report', 'view_stats'],
            'data' => ['import', 'export'],
        ];

        if (isset($actionMap[$actionType])) {
            return $query->whereIn('action', $actionMap[$actionType]);
        }

        return $query;
    }

    /**
     * Lấy tên action dễ đọc với mở rộng
     */
    public function getActionNameAttribute()
    {
        $actions = [
            'login' => 'Đăng nhập',
            'logout' => 'Đăng xuất',
            'create' => 'Tạo mới',
            'update' => 'Cập nhật',
            'delete' => 'Xóa',
            'view' => 'Xem',
            'view_report' => 'Xem báo cáo',
            'view_stats' => 'Xem thống kê',
            'export' => 'Xuất báo cáo',
            'import' => 'Nhập dữ liệu',
            'approve' => 'Phê duyệt',
            'reject' => 'Từ chối',
            'unknown' => 'Không xác định',
        ];

        return $actions[$this->action] ?? ucfirst($this->action);
    }

    /**
     * Lấy thông tin thiết bị từ user agent
     */
    public function getDeviceInfoAttribute()
    {
        if (!$this->user_agent) {
            return 'Không xác định';
        }

        // Phân tích user agent để lấy thông tin browser và OS
        $userAgent = $this->user_agent;

        // Detect browser
        $browser = 'Unknown';
        if (strpos($userAgent, 'Chrome') !== false) {
            preg_match('/Chrome\/([0-9.]+)/', $userAgent, $matches);
            $browser = 'Chrome ' . ($matches[1] ?? '');
        } elseif (strpos($userAgent, 'Firefox') !== false) {
            preg_match('/Firefox\/([0-9.]+)/', $userAgent, $matches);
            $browser = 'Firefox ' . ($matches[1] ?? '');
        } elseif (strpos($userAgent, 'Safari') !== false && strpos($userAgent, 'Chrome') === false) {
            preg_match('/Version\/([0-9.]+)/', $userAgent, $matches);
            $browser = 'Safari ' . ($matches[1] ?? '');
        } elseif (strpos($userAgent, 'Edge') !== false) {
            preg_match('/Edge\/([0-9.]+)/', $userAgent, $matches);
            $browser = 'Edge ' . ($matches[1] ?? '');
        }

        // Detect OS
        $os = 'Unknown';
        if (strpos($userAgent, 'Windows NT 10') !== false) {
            $os = 'Windows 10';
        } elseif (strpos($userAgent, 'Windows NT 11') !== false) {
            $os = 'Windows 11';
        } elseif (strpos($userAgent, 'Windows') !== false) {
            $os = 'Windows';
        } elseif (strpos($userAgent, 'Mac OS X') !== false) {
            preg_match('/Mac OS X ([0-9_]+)/', $userAgent, $matches);
            $os = 'macOS ' . str_replace('_', '.', $matches[1] ?? '');
        } elseif (strpos($userAgent, 'Ubuntu') !== false) {
            $os = 'Ubuntu';
        } elseif (strpos($userAgent, 'Linux') !== false) {
            $os = 'Linux';
        } elseif (strpos($userAgent, 'Android') !== false) {
            preg_match('/Android ([0-9.]+)/', $userAgent, $matches);
            $os = 'Android ' . ($matches[1] ?? '');
        } elseif (strpos($userAgent, 'iPhone') !== false || strpos($userAgent, 'iPad') !== false) {
            $os = 'iOS';
        }

        return $browser . ' / ' . $os;
    }

    /**
     * Lấy thời gian định dạng tiếng Việt
     */
    public function getFormattedTimeAttribute()
    {
        return $this->created_at->format('d/m/Y H:i:s');
    }

    /**
     * Lấy thời gian tương đối (bao lâu trước)
     */
    public function getTimeAgoAttribute()
    {
        return $this->created_at->diffForHumans();
    }

    /**
     * Kiểm tra xem có thay đổi dữ liệu không
     */
    public function getHasChangesAttribute()
    {
        return !empty($this->old_values) || !empty($this->new_values);
    }

    /**
     * Lấy tên model ngắn gọn
     */
    public function getModelNameAttribute()
    {
        if (!$this->model_type) {
            return null;
        }

        $modelNames = [
            'App\\Models\\API\\Taisan\\Congdap' => 'Cống đập',
            'App\\Models\\API\\Taisan\\Trambom' => 'Trạm bơm',
            'App\\Models\\API\\Taisan\\Kenhmuong' => 'Kênh mương',
            'App\\Models\\User' => 'Người dùng',
            'Spatie\\Permission\\Models\\Role' => 'Vai trò',
            'Spatie\\Permission\\Models\\Permission' => 'Quyền hạn',
            'App\\Models\\ActivityLog' => 'Lịch sử hoạt động',
        ];

        return $modelNames[$this->model_type] ?? class_basename($this->model_type);
    }

    /**
     * Lấy màu sắc cho action (để hiển thị UI)
     */
    public function getActionColorAttribute()
    {
        $colors = [
            'login' => 'green',
            'logout' => 'gray',
            'create' => 'blue',
            'update' => 'yellow',
            'delete' => 'red',
            'view' => 'purple',
            'view_report' => 'indigo',
            'view_stats' => 'pink',
            'export' => 'teal',
            'import' => 'orange',
        ];

        return $colors[$this->action] ?? 'gray';
    }

    /**
     * Lấy summary của thay đổi
     */
    public function getChangesSummaryAttribute()
    {
        if (!$this->has_changes) {
            return null;
        }

        $summary = [];

        if ($this->old_values && $this->new_values) {
            $changedFields = array_keys(array_diff_assoc($this->new_values, $this->old_values));
            $summary[] = 'Thay đổi: ' . implode(', ', $changedFields);
        } elseif ($this->new_values) {
            $summary[] = 'Tạo mới với ' . count($this->new_values) . ' trường';
        } elseif ($this->old_values) {
            $summary[] = 'Xóa ' . count($this->old_values) . ' trường';
        }

        return implode('; ', $summary);
    }
}
