<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ActivityLog extends Model
{
    protected $fillable = [
        'user_id',
        'action',
        'description',
        'model_type',
        'model_id',
        'old_values',
        'new_values',
        'ip_address',
        'user_agent',
    ];

    protected $casts = [
        'old_values' => 'array',
        'new_values' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Relationship với User
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Scope để lọc theo action
     */
    public function scopeByAction($query, $action)
    {
        return $query->where('action', $action);
    }

    /**
     * Scope để lọc theo user
     */
    public function scopeByUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Scope để lọc theo khoảng thời gian
     */
    public function scopeByDateRange($query, $dateFrom, $dateTo)
    {
        return $query->whereBetween('created_at', [$dateFrom, $dateTo]);
    }

    /**
     * Scope để tìm kiếm trong description
     */
    public function scopeSearch($query, $search)
    {
        return $query->where('description', 'ILIKE', '%' . $search . '%');
    }

    /**
     * Accessor để lấy tên action dễ đọc
     */
    public function getActionNameAttribute()
    {
        $actionMap = [
            'login' => 'Đăng nhập',
            'logout' => 'Đăng xuất',
            'create' => 'Tạo mới',
            'update' => 'Cập nhật',
            'delete' => 'Xóa',
            'view' => 'Xem',
            'export' => 'Xuất dữ liệu',
            'import' => 'Nhập dữ liệu',
            'assign_role' => 'Gán vai trò',
            'revoke_role' => 'Thu hồi vai trò',
            'assign_permission' => 'Gán quyền',
            'revoke_permission' => 'Thu hồi quyền',
            'approve' => 'Phê duyệt',
            'reject' => 'Từ chối',
        ];

        return $actionMap[$this->action] ?? $this->action;
    }

    /**
     * Accessor để lấy thông tin thiết bị từ user agent
     */
    public function getDeviceInfoAttribute()
    {
        if (!$this->user_agent) {
            return 'Không xác định';
        }

        $userAgent = $this->user_agent;

        // Detect browser
        $browser = 'Unknown';
        if (strpos($userAgent, 'Chrome') !== false && strpos($userAgent, 'Edge') === false) {
            preg_match('/Chrome\/([0-9.]+)/', $userAgent, $matches);
            $browser = 'Chrome ' . ($matches[1] ?? '');
        } elseif (strpos($userAgent, 'Firefox') !== false) {
            preg_match('/Firefox\/([0-9.]+)/', $userAgent, $matches);
            $browser = 'Firefox ' . ($matches[1] ?? '');
        } elseif (strpos($userAgent, 'Safari') !== false && strpos($userAgent, 'Chrome') === false) {
            preg_match('/Version\/([0-9.]+)/', $userAgent, $matches);
            $browser = 'Safari ' . ($matches[1] ?? '');
        } elseif (strpos($userAgent, 'Edge') !== false) {
            preg_match('/Edge\/([0-9.]+)/', $userAgent, $matches);
            $browser = 'Edge ' . ($matches[1] ?? '');
        }

        // Detect OS
        $os = 'Unknown';
        if (strpos($userAgent, 'Windows NT 10') !== false) {
            $os = 'Windows 10';
        } elseif (strpos($userAgent, 'Windows NT 11') !== false) {
            $os = 'Windows 11';
        } elseif (strpos($userAgent, 'Windows') !== false) {
            $os = 'Windows';
        } elseif (strpos($userAgent, 'Mac OS X') !== false) {
            preg_match('/Mac OS X ([0-9_]+)/', $userAgent, $matches);
            $os = 'macOS ' . str_replace('_', '.', $matches[1] ?? '');
        } elseif (strpos($userAgent, 'Ubuntu') !== false) {
            $os = 'Ubuntu';
        } elseif (strpos($userAgent, 'Linux') !== false) {
            $os = 'Linux';
        } elseif (strpos($userAgent, 'Android') !== false) {
            preg_match('/Android ([0-9.]+)/', $userAgent, $matches);
            $os = 'Android ' . ($matches[1] ?? '');
        } elseif (strpos($userAgent, 'iPhone') !== false || strpos($userAgent, 'iPad') !== false) {
            $os = 'iOS';
        }

        return $browser . ' / ' . $os;
    }

    /**
     * Static method để tạo activity log
     */
    public static function createLog(array $data)
    {
        return self::create([
            'user_id' => $data['user_id'] ?? auth()->id(),
            'action' => $data['action'],
            'description' => $data['description'],
            'model_type' => $data['model_type'] ?? null,
            'model_id' => $data['model_id'] ?? null,
            'old_values' => $data['old_values'] ?? null,
            'new_values' => $data['new_values'] ?? null,
            'ip_address' => $data['ip_address'] ?? request()->ip(),
            'user_agent' => $data['user_agent'] ?? request()->userAgent(),
        ]);
    }
}
