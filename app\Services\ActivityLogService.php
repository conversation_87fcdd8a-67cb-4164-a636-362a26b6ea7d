<?php

namespace App\Services;

use App\Events\AssetCreated;
use App\Events\AssetDeleted;
use App\Events\AssetUpdated;
use App\Events\ActivityLogged;
use Illuminate\Database\Eloquent\Model;

class ActivityLogService
{
    /**
     * Log asset creation
     */
    public static function logAssetCreated(Model $model, $user = null): void
    {
        event(new AssetCreated($model, $user));
    }

    /**
     * Log asset update
     */
    public static function logAssetUpdated(Model $model, array $oldValues, array $newValues, $user = null): void
    {
        event(new AssetUpdated($model, $oldValues, $newValues, $user));
    }

    /**
     * Log asset deletion
     */
    public static function logAssetDeleted(Model $model, array $oldValues, $user = null): void
    {
        event(new AssetDeleted($model, $oldValues, $user));
    }

    /**
     * Log custom activity
     */
    public static function logActivity(
        string $action,
        string $description,
        ?string $modelType = null,
        ?string $modelId = null,
        ?array $oldValues = null,
        ?array $newValues = null,
        $userId = null
    ): void {
        event(new ActivityLogged(
            $userId ?? auth()->id(),
            $action,
            $description,
            $modelType,
            $modelId,
            $oldValues,
            $newValues
        ));
    }

    /**
     * Log import activity
     */
    public static function logImport(string $modelType, int $successCount, int $errorCount, array $errors = []): void
    {
        $user = auth()->user();
        $userName = $user ? $user->name : 'Hệ thống';
        $modelName = self::getModelDisplayName($modelType);
        
        $description = "Người dùng {$userName} đã nhập {$successCount} {$modelName} thành công";
        if ($errorCount > 0) {
            $description .= ", {$errorCount} bản ghi lỗi";
        }

        self::logActivity(
            'import',
            $description,
            $modelType,
            null,
            null,
            [
                'success_count' => $successCount,
                'error_count' => $errorCount,
                'errors' => $errors
            ]
        );
    }

    /**
     * Log export activity
     */
    public static function logExport(string $modelType, int $recordCount, array $filters = []): void
    {
        $user = auth()->user();
        $userName = $user ? $user->name : 'Hệ thống';
        $modelName = self::getModelDisplayName($modelType);
        
        $description = "Người dùng {$userName} đã xuất {$recordCount} {$modelName}";
        if (!empty($filters)) {
            $description .= " với bộ lọc: " . json_encode($filters);
        }

        self::logActivity(
            'export',
            $description,
            $modelType,
            null,
            null,
            [
                'record_count' => $recordCount,
                'filters' => $filters
            ]
        );
    }

    /**
     * Get display name for model
     */
    private static function getModelDisplayName(string $modelClass): string
    {
        $modelMap = [
            'App\Models\API\Taisan\Congdap' => 'Cống đập',
            'App\Models\API\Taisan\Trambom' => 'Trạm bơm',
            'App\Models\API\Taisan\Kenhmuong' => 'Kênh mương',
            'App\Models\User' => 'Người dùng',
            'Spatie\Permission\Models\Role' => 'Vai trò',
            'Spatie\Permission\Models\Permission' => 'Quyền',
        ];

        return $modelMap[$modelClass] ?? class_basename($modelClass);
    }
}
