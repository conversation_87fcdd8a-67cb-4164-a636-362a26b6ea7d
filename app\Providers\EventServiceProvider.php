<?php

namespace App\Providers;

use App\Events\ActivityLogged;
use App\Events\AssetCreated;
use App\Events\AssetDeleted;
use App\Events\AssetUpdated;
use App\Events\UserLoggedIn;
use App\Events\UserLoggedOut;
use App\Listeners\LogActivityListener;
use App\Listeners\LogAssetCreated;
use App\Listeners\LogAssetDeleted;
use App\Listeners\LogAssetUpdated;
use App\Listeners\LogUserLogin;
use App\Listeners\LogUserLogout;
use Illuminate\Auth\Events\Registered;
use Illuminate\Auth\Listeners\SendEmailVerificationNotification;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Event;

class EventServiceProvider extends ServiceProvider
{
    /**
     * The event to listener mappings for the application.
     *
     * @var array<class-string, array<int, class-string>>
     */
    protected $listen = [
        Registered::class => [
            SendEmailVerificationNotification::class,
        ],

        // Activity Logging Events
        ActivityLogged::class => [
            LogActivityListener::class,
        ],

        UserLoggedIn::class => [
            LogUserLogin::class,
        ],

        UserLoggedOut::class => [
            LogUserLogout::class,
        ],

        AssetCreated::class => [
            LogAssetCreated::class,
        ],

        AssetUpdated::class => [
            LogAssetUpdated::class,
        ],

        AssetDeleted::class => [
            LogAssetDeleted::class,
        ],
    ];

    /**
     * Register any events for your application.
     */
    public function boot(): void
    {
        //
    }

    /**
     * Determine if events and listeners should be automatically discovered.
     */
    public function shouldDiscoverEvents(): bool
    {
        return false;
    }
}
