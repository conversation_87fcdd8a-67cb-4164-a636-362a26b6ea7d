<?php

namespace App\Listeners;

use App\Events\ActivityLogged;
use App\Events\AssetDeleted;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class LogAssetDeleted implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * Handle the event.
     */
    public function handle(AssetDeleted $event): void
    {
        $modelClass = get_class($event->model);
        $modelName = $this->getModelDisplayName($modelClass);
        $assetName = $event->oldValues['ten'] ?? $event->oldValues['name'] ?? $event->model->id;
        
        $userName = $event->user ? $event->user->name : '<PERSON>ệ thống';
        $description = "Người dùng {$userName} đã xóa {$modelName}: {$assetName}";

        event(new ActivityLogged(
            $event->user?->id,
            'delete',
            $description,
            $modelClass,
            $event->model->id,
            $event->oldValues,
            null,
            $event->ipAddress,
            $event->userAgent
        ));
    }

    /**
     * Get display name for model
     */
    private function getModelDisplayName(string $modelClass): string
    {
        $modelMap = [
            'App\Models\API\Taisan\Congdap' => 'Cống đập',
            'App\Models\API\Taisan\Trambom' => 'Trạm bơm',
            'App\Models\API\Taisan\Kenhmuong' => 'Kênh mương',
            'App\Models\User' => 'Người dùng',
            'Spatie\Permission\Models\Role' => 'Vai trò',
            'Spatie\Permission\Models\Permission' => 'Quyền',
        ];

        return $modelMap[$modelClass] ?? class_basename($modelClass);
    }
}
