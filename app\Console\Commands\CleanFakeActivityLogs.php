<?php

namespace App\Console\Commands;

use App\Models\ActivityLog;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class CleanFakeActivityLogs extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'activity-log:clean-fake 
                            {--dry-run : Show what would be deleted without actually deleting}
                            {--force : Force deletion without confirmation}';

    /**
     * The console command description.
     */
    protected $description = 'Clean fake activity logs created by seeders (production safe)';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🧹 Activity Log Fake Data Cleaner');
        $this->info('=================================');

        // Find fake data patterns
        $fakePatterns = [
            '[FAKE DATA]',
            '[TEST]',
            '<EMAIL>',
            '<EMAIL>', 
            '<EMAIL>',
            'Test Browser',
        ];

        $this->info('🔍 Scanning for fake activity logs...');

        // Build query to find fake logs
        $query = ActivityLog::query();
        
        foreach ($fakePatterns as $pattern) {
            $query->orWhere('description', 'like', "%{$pattern}%")
                  ->orWhere('user_agent', 'like', "%{$pattern}%");
        }

        // Also find logs from test users
        $query->orWhereHas('user', function ($userQuery) {
            $userQuery->where('email', 'like', '%@example.com')
                     ->orWhere('name', 'like', '%(Test)%');
        });

        $fakeLogs = $query->get();
        $fakeCount = $fakeLogs->count();

        if ($fakeCount === 0) {
            $this->info('✅ No fake activity logs found.');
            return 0;
        }

        $this->warn("Found {$fakeCount} fake activity logs:");

        // Show sample of fake logs
        $this->table(
            ['ID', 'User', 'Action', 'Description', 'Created At'],
            $fakeLogs->take(10)->map(function ($log) {
                return [
                    $log->id,
                    $log->user ? $log->user->name : 'N/A',
                    $log->action,
                    substr($log->description, 0, 50) . '...',
                    $log->created_at->format('Y-m-d H:i:s')
                ];
            })->toArray()
        );

        if ($fakeCount > 10) {
            $this->line("... and " . ($fakeCount - 10) . " more");
        }

        // Dry run mode
        if ($this->option('dry-run')) {
            $this->info('🔍 DRY RUN MODE: No data will be deleted.');
            $this->info("Would delete {$fakeCount} fake activity logs.");
            return 0;
        }

        // Confirmation
        if (!$this->option('force')) {
            if (!$this->confirm("Are you sure you want to delete {$fakeCount} fake activity logs?")) {
                $this->info('❌ Operation cancelled.');
                return 0;
            }
        }

        // Delete fake logs
        $this->info('🗑️  Deleting fake activity logs...');
        
        DB::transaction(function () use ($query) {
            $deletedCount = $query->delete();
            $this->info("✅ Deleted {$deletedCount} fake activity logs.");
        });

        // Show statistics after cleanup
        $this->newLine();
        $this->info('📊 Activity Logs Statistics After Cleanup:');
        $totalLogs = ActivityLog::count();
        $todayLogs = ActivityLog::whereDate('created_at', today())->count();
        $thisWeekLogs = ActivityLog::whereBetween('created_at', [now()->startOfWeek(), now()->endOfWeek()])->count();

        $this->table(
            ['Metric', 'Count'],
            [
                ['Total Activity Logs', $totalLogs],
                ['Today\'s Logs', $todayLogs],
                ['This Week\'s Logs', $thisWeekLogs],
            ]
        );

        $this->info('🎉 Fake data cleanup completed successfully!');
        
        return 0;
    }
}
