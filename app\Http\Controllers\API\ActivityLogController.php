<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\ActivityLog;
use App\Models\User;
use App\Traits\ApiResponseTrait;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Throwable;

class ActivityLogController extends Controller
{
    use ApiResponseTrait;

    /**
     * Display a listing of activity logs with filtering, pagination and caching
     */
    public function index(Request $request)
    {
        try {
            // Generate cache key based on request parameters
            $cacheKey = $this->generateCacheKey($request);

            // Try to get from cache first
            $cachedResult = $this->getCachedData($cacheKey);
            if ($cachedResult) {
                return $this->successResponse(
                    data: $cachedResult,
                    message: 'L<PERSON>y danh sách lịch sử hoạt động thành công (cached)'
                );
            }

            $query = ActivityLog::with('user:id,name,email');

            // Apply filters using enhanced scopes
            $this->applyFilters($query, $request);

            // Order by latest first
            $query->orderBy('created_at', 'desc');

            // Paginate results
            $perPage = $request->get('per_page', 15);
            $logs = $query->paginate($perPage);

            // Transform data for response with enhanced data
            $transformedLogs = $logs->getCollection()->map(function ($log) {
                return [
                    'id' => $log->id,
                    'time' => $log->formatted_time,
                    'time_ago' => $log->time_ago,
                    'user' => $log->user ? $log->user->name : 'Hệ thống',
                    'user_email' => $log->user ? $log->user->email : null,
                    'action' => $log->action_name,
                    'action_raw' => $log->action,
                    'action_color' => $log->action_color,
                    'description' => $log->description,
                    'ip' => $log->ip_address ?? 'Không xác định',
                    'device' => $log->device_info,
                    'model_type' => $log->model_type,
                    'model_name' => $log->model_name,
                    'model_id' => $log->model_id,
                    'old_values' => $log->old_values,
                    'new_values' => $log->new_values,
                    'has_changes' => $log->has_changes,
                    'changes_summary' => $log->changes_summary,
                ];
            });

            // Replace collection with transformed data
            $logs->setCollection($transformedLogs);

            // Prepare response data
            $responseData = [
                'data' => $logs->items(),
                'meta' => [
                    'current_page' => $logs->currentPage(),
                    'per_page' => $logs->perPage(),
                    'total' => $logs->total(),
                    'last_page' => $logs->lastPage(),
                    'from' => $logs->firstItem(),
                    'to' => $logs->lastItem(),
                ],
                'links' => [
                    'first' => $logs->url(1),
                    'last' => $logs->url($logs->lastPage()),
                    'prev' => $logs->previousPageUrl(),
                    'next' => $logs->nextPageUrl(),
                ]
            ];

            // Cache the result
            $this->cacheData($cacheKey, $responseData);

            return $this->successResponse(
                data: $responseData,
                message: 'Lấy danh sách lịch sử hoạt động thành công'
            );

        } catch (Throwable $e) {
            Log::error('Error fetching activity logs: ' . $e->getMessage());
            return $this->errorResponse('Lỗi khi lấy danh sách lịch sử hoạt động', 500);
        }
    }

    /**
     * Display the specified activity log
     */
    public function show(int $id)
    {
        try {
            $log = ActivityLog::with('user:id,name,email')->findOrFail($id);

            $transformedLog = [
                'id' => $log->id,
                'time' => $log->created_at->format('d/m/Y H:i:s'),
                'user' => $log->user ? $log->user->name : 'Hệ thống',
                'user_email' => $log->user ? $log->user->email : null,
                'action' => $log->action_name,
                'action_raw' => $log->action,
                'description' => $log->description,
                'ip' => $log->ip_address ?? 'Không xác định',
                'device' => $log->device_info,
                'model_type' => $log->model_type,
                'model_id' => $log->model_id,
                'old_values' => $log->old_values,
                'new_values' => $log->new_values,
                'created_at' => $log->created_at,
                'updated_at' => $log->updated_at,
            ];

            return $this->successResponse(
                data: $transformedLog,
                message: 'Lấy thông tin lịch sử hoạt động thành công'
            );

        } catch (Throwable $e) {
            Log::error('Error fetching activity log ID ' . $id . ': ' . $e->getMessage());
            return $this->errorResponse('Lỗi khi lấy thông tin lịch sử hoạt động', 500);
        }
    }

    /**
     * Generate cache key based on request parameters
     */
    private function generateCacheKey(Request $request): string
    {
        $params = [
            'page' => $request->get('page', 1),
            'per_page' => $request->get('per_page', 15),
            'user_id' => $request->get('user_id'),
            'action' => $request->get('action'),
            'search' => $request->get('search'),
            'date_from' => $request->get('date_from'),
            'date_to' => $request->get('date_to'),
            'model_type' => $request->get('model_type'),
        ];

        return 'activity_logs_' . md5(serialize($params));
    }

    /**
     * Get cached data with fallback for different cache drivers
     */
    private function getCachedData(string $cacheKey)
    {
        try {
            // Try cache tags first (Redis, Memcached)
            if (method_exists(Cache::getStore(), 'tags')) {
                return Cache::tags(['activity-logs-list'])->get($cacheKey);
            } else {
                // Fallback for file/database cache
                return Cache::get($cacheKey);
            }
        } catch (\Exception $e) {
            Log::warning('Cache retrieval failed: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Cache data with appropriate TTL and tags
     */
    private function cacheData(string $cacheKey, $data): void
    {
        try {
            $ttl = 300; // 5 minutes

            if (method_exists(Cache::getStore(), 'tags')) {
                Cache::tags(['activity-logs-list'])->put($cacheKey, $data, $ttl);
            } else {
                Cache::put($cacheKey, $data, $ttl);
            }
        } catch (\Exception $e) {
            Log::warning('Cache storage failed: ' . $e->getMessage());
        }
    }

    /**
     * Apply filters to query with enhanced filtering
     */
    private function applyFilters($query, Request $request): void
    {
        if ($request->filled('user_id')) {
            $query->byUser($request->user_id);
        }

        if ($request->filled('action')) {
            $query->byAction($request->action);
        }

        if ($request->filled('search')) {
            $query->search($request->search);
        }

        if ($request->filled('date_from') && $request->filled('date_to')) {
            $query->byDateRange($request->date_from, $request->date_to);
        }

        if ($request->filled('model_type')) {
            $query->byModelType($request->model_type);
        }

        if ($request->filled('ip_address')) {
            $query->byIpAddress($request->ip_address);
        }

        if ($request->filled('action_type')) {
            $query->byActionType($request->action_type);
        }

        if ($request->filled('recent_days')) {
            $query->recent($request->recent_days);
        }
    }

    /**
     * Get available users for filtering with caching
     */
    public function getUsers()
    {
        try {
            $cacheKey = 'activity_logs_users';

            // Try to get from cache
            $users = $this->getCachedData($cacheKey);

            if (!$users) {
                $users = User::select('id', 'name', 'email')
                    ->whereHas('activityLogs')
                    ->orderBy('name')
                    ->get();

                // Cache for 1 hour
                $this->cacheData($cacheKey, $users);
            }

            return $this->successResponse(
                data: $users,
                message: 'Lấy danh sách người dùng thành công'
            );

        } catch (Throwable $e) {
            Log::error('Error fetching users for activity logs: ' . $e->getMessage());
            return $this->errorResponse('Lỗi khi lấy danh sách người dùng', 500);
        }
    }

    /**
     * Get available actions for filtering with caching and enhanced data
     */
    public function getActions()
    {
        try {
            $cacheKey = 'activity_logs_actions';

            // Try to get from cache
            $actions = $this->getCachedData($cacheKey);

            if (!$actions) {
                $actions = ActivityLog::select('action')
                    ->distinct()
                    ->orderBy('action')
                    ->pluck('action')
                    ->map(function ($action) {
                        $actionNames = [
                            'login' => 'Đăng nhập',
                            'logout' => 'Đăng xuất',
                            'create' => 'Tạo mới',
                            'update' => 'Cập nhật',
                            'delete' => 'Xóa',
                            'view' => 'Xem',
                            'view_report' => 'Xem báo cáo',
                            'view_stats' => 'Xem thống kê',
                            'export' => 'Xuất báo cáo',
                            'import' => 'Nhập dữ liệu',
                            'approve' => 'Phê duyệt',
                            'reject' => 'Từ chối',
                            'unknown' => 'Không xác định',
                        ];

                        // Get color for UI
                        $colors = [
                            'login' => 'green',
                            'logout' => 'gray',
                            'create' => 'blue',
                            'update' => 'yellow',
                            'delete' => 'red',
                            'view' => 'purple',
                            'view_report' => 'indigo',
                            'view_stats' => 'pink',
                            'export' => 'teal',
                            'import' => 'orange',
                        ];

                        return [
                            'value' => $action,
                            'label' => $actionNames[$action] ?? ucfirst($action),
                            'color' => $colors[$action] ?? 'gray'
                        ];
                    });

                // Cache for 1 hour
                $this->cacheData($cacheKey, $actions);
            }

            return $this->successResponse(
                data: $actions,
                message: 'Lấy danh sách loại hoạt động thành công'
            );

        } catch (Throwable $e) {
            Log::error('Error fetching actions for activity logs: ' . $e->getMessage());
            return $this->errorResponse('Lỗi khi lấy danh sách loại hoạt động', 500);
        }
    }

    /**
     * Get statistics for dashboard
     */
    public function getStats()
    {
        try {
            $cacheKey = 'activity_logs_stats';

            // Try to get from cache
            $stats = $this->getCachedData($cacheKey);

            if (!$stats) {
                $stats = [
                    'total_logs' => ActivityLog::count(),
                    'today_logs' => ActivityLog::whereDate('created_at', today())->count(),
                    'this_week_logs' => ActivityLog::whereBetween('created_at', [now()->startOfWeek(), now()->endOfWeek()])->count(),
                    'this_month_logs' => ActivityLog::whereMonth('created_at', now()->month)->count(),
                    'top_actions' => ActivityLog::select('action')
                        ->selectRaw('count(*) as count')
                        ->groupBy('action')
                        ->orderByDesc('count')
                        ->limit(5)
                        ->get(),
                    'top_users' => ActivityLog::with('user:id,name')
                        ->select('user_id')
                        ->selectRaw('count(*) as count')
                        ->groupBy('user_id')
                        ->orderByDesc('count')
                        ->limit(5)
                        ->get(),
                ];

                // Cache for 15 minutes
                $this->cacheData($cacheKey, $stats);
            }

            return $this->successResponse(
                data: $stats,
                message: 'Lấy thống kê hoạt động thành công'
            );

        } catch (Throwable $e) {
            Log::error('Error fetching activity logs stats: ' . $e->getMessage());
            return $this->errorResponse('Lỗi khi lấy thống kê hoạt động', 500);
        }
    }
}
