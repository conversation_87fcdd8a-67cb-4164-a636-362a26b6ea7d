<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\ActivityLog;
use App\Models\User;
use App\Traits\ApiResponseTrait;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Throwable;

class ActivityLogController extends Controller
{
    use ApiResponseTrait;

    /**
     * Display a listing of activity logs with filtering and pagination
     */
    public function index(Request $request)
    {
        try {
            // Create cache key based on request parameters
            $cacheKey = 'activity_logs_page_' . md5(serialize($request->all()));

            // Try to get from cache first
            $result = $this->getCachedData($cacheKey, function() use ($request) {
                $query = ActivityLog::with('user:id,name,email');

                // Apply filters
                if ($request->filled('user_id')) {
                    $query->byUser($request->user_id);
                }

                if ($request->filled('action')) {
                    $query->byAction($request->action);
                }

                if ($request->filled('search')) {
                    $query->search($request->search);
                }

                if ($request->filled('date_from') && $request->filled('date_to')) {
                    $query->byDateRange($request->date_from, $request->date_to);
                }

                if ($request->filled('model_type')) {
                    $query->where('model_type', $request->model_type);
                }

                // Order by latest first
                $query->orderBy('created_at', 'desc');

                // Paginate results
                $perPage = $request->get('per_page', 15);
                return $query->paginate($perPage);
            });

            $logs = $result;

            // Transform data for response
            $transformedLogs = $logs->getCollection()->map(function ($log) {
                return [
                    'id' => $log->id,
                    'time' => $log->created_at->format('d/m/Y H:i:s'),
                    'user' => $log->user ? $log->user->name : 'Hệ thống',
                    'user_email' => $log->user ? $log->user->email : null,
                    'action' => $log->action_name,
                    'action_raw' => $log->action,
                    'description' => $log->description,
                    'ip' => $log->ip_address ?? 'Không xác định',
                    'device' => $log->device_info,
                    'model_type' => $log->model_type,
                    'model_id' => $log->model_id,
                    'old_values' => $log->old_values,
                    'new_values' => $log->new_values,
                ];
            });

            // Replace collection with transformed data
            $logs->setCollection($transformedLogs);

            return response()->json([
                'success' => true,
                'message' => 'Lấy danh sách lịch sử hoạt động thành công',
                'data' => $logs->items(),
                'meta' => [
                    'current_page' => $logs->currentPage(),
                    'per_page' => $logs->perPage(),
                    'total' => $logs->total(),
                    'last_page' => $logs->lastPage(),
                    'from' => $logs->firstItem(),
                    'to' => $logs->lastItem(),
                ],
                'links' => [
                    'first' => $logs->url(1),
                    'last' => $logs->url($logs->lastPage()),
                    'prev' => $logs->previousPageUrl(),
                    'next' => $logs->nextPageUrl(),
                ]
            ]);

        } catch (Throwable $e) {
            Log::error('Error fetching activity logs: ' . $e->getMessage());
            return $this->errorResponse('Lỗi khi lấy danh sách lịch sử hoạt động', 500);
        }
    }

    /**
     * Display the specified activity log
     */
    public function show(int $id)
    {
        try {
            $log = ActivityLog::with('user:id,name,email')->findOrFail($id);

            $transformedLog = [
                'id' => $log->id,
                'time' => $log->created_at->format('d/m/Y H:i:s'),
                'user' => $log->user ? $log->user->name : 'Hệ thống',
                'user_email' => $log->user ? $log->user->email : null,
                'action' => $log->action_name,
                'action_raw' => $log->action,
                'description' => $log->description,
                'ip' => $log->ip_address ?? 'Không xác định',
                'device' => $log->device_info,
                'model_type' => $log->model_type,
                'model_id' => $log->model_id,
                'old_values' => $log->old_values,
                'new_values' => $log->new_values,
                'created_at' => $log->created_at,
                'updated_at' => $log->updated_at,
            ];

            return $this->successResponse(
                data: $transformedLog,
                message: 'Lấy thông tin lịch sử hoạt động thành công'
            );

        } catch (Throwable $e) {
            Log::error('Error fetching activity log ID ' . $id . ': ' . $e->getMessage());
            return $this->errorResponse('Lỗi khi lấy thông tin lịch sử hoạt động', 500);
        }
    }

    /**
     * Get available users for filtering
     */
    public function getUsers()
    {
        try {
            $users = $this->getCachedData('activity_logs_users', function() {
                return User::select('id', 'name', 'email')
                    ->whereHas('activityLogs')
                    ->orderBy('name')
                    ->get();
            }, 30); // Cache for 30 minutes

            return $this->successResponse(
                data: $users,
                message: 'Lấy danh sách người dùng thành công'
            );

        } catch (Throwable $e) {
            Log::error('Error fetching users for activity logs: ' . $e->getMessage());
            return $this->errorResponse('Lỗi khi lấy danh sách người dùng', 500);
        }
    }

    /**
     * Get available actions for filtering
     */
    public function getActions()
    {
        try {
            $actions = $this->getCachedData('activity_logs_actions', function() {
                return ActivityLog::select('action')
                    ->distinct()
                    ->orderBy('action')
                    ->pluck('action')
                    ->map(function ($action) {
                        $actionNames = [
                            'login' => 'Đăng nhập',
                            'logout' => 'Đăng xuất',
                            'create' => 'Tạo mới',
                            'update' => 'Cập nhật',
                            'delete' => 'Xóa',
                            'view' => 'Xem',
                            'export' => 'Xuất báo cáo',
                            'import' => 'Nhập dữ liệu',
                            'approve' => 'Phê duyệt',
                            'reject' => 'Từ chối',
                        ];

                        return [
                            'value' => $action,
                            'label' => $actionNames[$action] ?? $action
                        ];
                    });
            }, 30); // Cache for 30 minutes

            return $this->successResponse(
                data: $actions,
                message: 'Lấy danh sách loại hoạt động thành công'
            );

        } catch (Throwable $e) {
            Log::error('Error fetching actions for activity logs: ' . $e->getMessage());
            return $this->errorResponse('Lỗi khi lấy danh sách loại hoạt động', 500);
        }
    }

    /**
     * Get cached data with fallback
     */
    private function getCachedData(string $key, callable $callback, int $minutes = 5)
    {
        try {
            // Try cache tags first
            return Cache::tags(['activity-logs-list'])->remember($key, $minutes * 60, $callback);
        } catch (\Exception $e) {
            // Fallback for drivers that don't support tags
            return Cache::remember($key, $minutes * 60, $callback);
        }
    }

    /**
     * Clear activity logs cache
     */
    private function clearActivityLogsCache(): void
    {
        try {
            // Try cache tags first
            Cache::tags(['activity-logs-list'])->flush();
        } catch (\Exception $e) {
            // Fallback for drivers that don't support tags
            $keys = [
                'activity_logs_page_*',
                'activity_logs_users',
                'activity_logs_actions',
            ];

            foreach ($keys as $pattern) {
                if (strpos($pattern, '*') !== false) {
                    // For wildcard patterns, we need to implement a custom solution
                    for ($i = 1; $i <= 100; $i++) {
                        Cache::forget(str_replace('*', $i, $pattern));
                    }
                } else {
                    Cache::forget($pattern);
                }
            }
        }
    }
}
