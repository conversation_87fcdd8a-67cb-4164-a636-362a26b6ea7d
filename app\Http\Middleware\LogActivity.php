<?php

namespace App\Http\Middleware;

use App\Models\ActivityLog;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Symfony\Component\HttpFoundation\Response;

class LogActivity
{
    /**
     * Unique request identifier to prevent duplicate logging
     */
    private static array $processedRequests = [];

    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Capture request data before processing for old_values
        $oldValues = $this->captureOldValues($request);

        $response = $next($request);

        // Only log for authenticated users and successful responses
        if (Auth::check() && $response->getStatusCode() < 400) {
            $this->logActivity($request, $response, $oldValues);
        }

        return $response;
    }

    /**
     * Capture old values before request processing
     */
    private function captureOldValues(Request $request): ?array
    {
        try {
            $method = $request->method();
            $path = $request->path();

            // Only capture for UPDATE and DELETE operations
            if (!in_array($method, ['PUT', 'PATCH', 'DELETE'])) {
                return null;
            }

            // Extract model ID from path
            $modelId = $this->extractModelId($path, $request);
            $modelType = $this->extractModelType($path);

            if (!$modelId || !$modelType) {
                return null;
            }

            // Get the model instance
            if (class_exists($modelType)) {
                $model = $modelType::find($modelId);
                return $model ? $model->toArray() : null;
            }

            return null;
        } catch (\Exception $e) {
            Log::error('Failed to capture old values: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Log the activity with duplicate prevention
     */
    private function logActivity(Request $request, Response $response, ?array $oldValues = null): void
    {
        try {
            $method = $request->method();
            $path = $request->path();
            $user = Auth::user();

            // Generate unique request identifier to prevent duplicates
            $requestId = $this->generateRequestId($request, $user);

            // Check if this request has already been logged
            if (in_array($requestId, self::$processedRequests)) {
                return;
            }

            // Skip logging for certain paths
            if ($this->shouldSkipLogging($path)) {
                return;
            }

            // Determine action and generate description
            $action = $this->determineAction($method, $path);
            $description = $this->generateDescription($method, $path, $request);
            $newValues = $this->captureNewValues($request, $method);

            // Create activity log using the enhanced method
            ActivityLog::createLog([
                'user_id' => $user->id,
                'action' => $action,
                'description' => $description,
                'ip_address' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'model_type' => $this->extractModelType($path),
                'model_id' => $this->extractModelId($path, $request),
                'old_values' => $oldValues,
                'new_values' => $newValues,
            ]);

            // Mark this request as processed
            self::$processedRequests[] = $requestId;

            // Invalidate activity logs cache
            $this->invalidateActivityLogsCache();

        } catch (\Exception $e) {
            // Don't let logging errors break the application
            Log::error('Failed to log activity: ' . $e->getMessage());
        }
    }

    /**
     * Generate unique request identifier for duplicate prevention
     */
    private function generateRequestId(Request $request, $user): string
    {
        return md5(
            $user->id .
            $request->method() .
            $request->path() .
            $request->ip() .
            json_encode($request->all()) .
            microtime(true)
        );
    }

    /**
     * Check if logging should be skipped for this path
     */
    private function shouldSkipLogging(string $path): bool
    {
        $skipPaths = [
            'api/activity-logs',
            'api/activity-logs/filters',
            'sanctum/csrf-cookie',
            'livewire',
            'api/csrf-token',
            'api/user', // Skip user profile checks
        ];

        foreach ($skipPaths as $skipPath) {
            if (str_starts_with($path, $skipPath)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Capture new values from request data
     */
    private function captureNewValues(Request $request, string $method): ?array
    {
        // Only capture for CREATE and UPDATE operations
        if (!in_array($method, ['POST', 'PUT', 'PATCH'])) {
            return null;
        }

        $data = $request->all();

        // Remove sensitive data
        $sensitiveFields = ['password', 'password_confirmation', '_token', '_method'];
        foreach ($sensitiveFields as $field) {
            unset($data[$field]);
        }

        return !empty($data) ? $data : null;
    }

    /**
     * Invalidate activity logs cache
     */
    private function invalidateActivityLogsCache(): void
    {
        try {
            // Try to use cache tags if supported
            if (method_exists(Cache::getStore(), 'tags')) {
                Cache::tags(['activity-logs-list'])->flush();
            } else {
                // Fallback for cache drivers that don't support tags
                Cache::forget('activity_logs_list');
                Cache::forget('activity_logs_users');
                Cache::forget('activity_logs_actions');
            }
        } catch (\Exception $e) {
            Log::error('Failed to invalidate activity logs cache: ' . $e->getMessage());
        }
    }

    /**
     * Determine action based on HTTP method and path
     */
    private function determineAction(string $method, string $path): string
    {
        // Authentication actions
        if (str_contains($path, 'login')) {
            return 'login';
        }

        if (str_contains($path, 'logout')) {
            return 'logout';
        }

        // Import/Export actions
        if (str_contains($path, 'export')) {
            return 'export';
        }

        if (str_contains($path, 'import')) {
            return 'import';
        }

        // Specific resource actions
        if (str_contains($path, 'reports')) {
            return 'view_report';
        }

        if (str_contains($path, 'stats')) {
            return 'view_stats';
        }

        // Standard CRUD operations
        return match ($method) {
            'GET' => 'view',
            'POST' => 'create',
            'PUT', 'PATCH' => 'update',
            'DELETE' => 'delete',
            default => 'unknown'
        };
    }

    /**
     * Generate detailed description for the activity
     */
    private function generateDescription(string $method, string $path, Request $request): string
    {
        $user = Auth::user();
        $userName = $user->name;
        $userEmail = $user->email;

        // Authentication actions
        if (str_contains($path, 'login')) {
            return "Người dùng {$userName} ({$userEmail}) đăng nhập vào hệ thống từ IP {$request->ip()}";
        }

        if (str_contains($path, 'logout')) {
            return "Người dùng {$userName} ({$userEmail}) đăng xuất khỏi hệ thống";
        }

        // Extract resource information
        $resourceName = $this->extractResourceName($path);
        $modelId = $this->extractModelId($path, $request);
        $resourceDetail = $modelId ? " (ID: {$modelId})" : "";

        // Import/Export actions
        if (str_contains($path, 'import')) {
            $fileName = $request->file('file') ? $request->file('file')->getClientOriginalName() : 'file';
            return "Người dùng {$userName} nhập dữ liệu {$resourceName} từ file {$fileName}";
        }

        if (str_contains($path, 'export')) {
            return "Người dùng {$userName} xuất báo cáo {$resourceName}";
        }

        // Report and stats actions
        if (str_contains($path, 'reports')) {
            return "Người dùng {$userName} xem báo cáo {$resourceName}";
        }

        if (str_contains($path, 'stats')) {
            return "Người dùng {$userName} xem thống kê {$resourceName}";
        }

        // Standard CRUD operations with detailed descriptions
        return match ($method) {
            'GET' => $this->generateViewDescription($userName, $resourceName, $path, $modelId),
            'POST' => $this->generateCreateDescription($userName, $resourceName, $request),
            'PUT', 'PATCH' => $this->generateUpdateDescription($userName, $resourceName, $modelId, $request),
            'DELETE' => $this->generateDeleteDescription($userName, $resourceName, $modelId),
            default => "Người dùng {$userName} thực hiện hành động không xác định trên {$resourceName}{$resourceDetail}"
        };
    }

    /**
     * Generate detailed view description
     */
    private function generateViewDescription(string $userName, string $resourceName, string $path, ?string $modelId): string
    {
        if ($modelId) {
            return "Người dùng {$userName} xem chi tiết {$resourceName} (ID: {$modelId})";
        }

        // Check for specific view types
        if (str_contains($path, 'filters')) {
            return "Người dùng {$userName} lấy danh sách bộ lọc cho {$resourceName}";
        }

        return "Người dùng {$userName} xem danh sách {$resourceName}";
    }

    /**
     * Generate detailed create description
     */
    private function generateCreateDescription(string $userName, string $resourceName, Request $request): string
    {
        $details = [];

        // Add specific details based on resource type
        if (str_contains($resourceName, 'người dùng') && $request->has('name')) {
            $details[] = "tên: {$request->input('name')}";
        }

        if (str_contains($resourceName, 'vai trò') && $request->has('name')) {
            $details[] = "tên vai trò: {$request->input('name')}";
        }

        $detailString = !empty($details) ? " (" . implode(', ', $details) . ")" : "";

        return "Người dùng {$userName} tạo mới {$resourceName}{$detailString}";
    }

    /**
     * Generate detailed update description
     */
    private function generateUpdateDescription(string $userName, string $resourceName, ?string $modelId, Request $request): string
    {
        $details = [];
        $resourceDetail = $modelId ? " (ID: {$modelId})" : "";

        // Add specific update details
        if ($request->has('name')) {
            $details[] = "cập nhật tên";
        }

        if ($request->has('email')) {
            $details[] = "cập nhật email";
        }

        if ($request->has('roles')) {
            $details[] = "cập nhật vai trò";
        }

        $detailString = !empty($details) ? " - " . implode(', ', $details) : "";

        return "Người dùng {$userName} cập nhật {$resourceName}{$resourceDetail}{$detailString}";
    }

    /**
     * Generate detailed delete description
     */
    private function generateDeleteDescription(string $userName, string $resourceName, ?string $modelId): string
    {
        $resourceDetail = $modelId ? " (ID: {$modelId})" : "";
        return "Người dùng {$userName} xóa {$resourceName}{$resourceDetail}";
    }

    /**
     * Extract detailed resource name from path
     */
    private function extractResourceName(string $path): string
    {
        $resourceMap = [
            // Taisan resources
            'congdap' => 'cống đập',
            'trambom' => 'trạm bơm',
            'kenhmuong' => 'kênh mương',

            // User management
            'users' => 'người dùng',
            'roles' => 'vai trò',
            'permissions' => 'quyền hạn',

            // Reports and statistics
            'reports/b1a' => 'báo cáo B1A',
            'reports/asset-stats' => 'thống kê tài sản',
            'reports' => 'báo cáo',
            'stats' => 'thống kê',

            // Import/Export
            'import' => 'nhập dữ liệu',
            'export' => 'xuất dữ liệu',

            // Spatial and location
            'spatial' => 'dữ liệu không gian',
            'basemap' => 'bản đồ nền',

            // Templates and configurations
            'asset-template' => 'mẫu tài sản',
            'quyet-toan' => 'quyết toán',

            // Activity logs
            'activity-logs' => 'lịch sử hoạt động',
        ];

        // Check for exact matches first (longer paths)
        foreach ($resourceMap as $key => $name) {
            if (str_contains($path, $key)) {
                return $name;
            }
        }

        // Fallback to generic resource name
        return 'tài nguyên hệ thống';
    }

    /**
     * Extract model type from path with enhanced mapping
     */
    private function extractModelType(string $path): ?string
    {
        $modelMap = [
            // Taisan models
            'congdap' => 'App\\Models\\API\\Taisan\\Congdap',
            'trambom' => 'App\\Models\\API\\Taisan\\Trambom',
            'kenhmuong' => 'App\\Models\\API\\Taisan\\Kenhmuong',

            // User management models
            'users' => 'App\\Models\\User',
            'roles' => 'Spatie\\Permission\\Models\\Role',
            'permissions' => 'Spatie\\Permission\\Models\\Permission',

            // Activity logs
            'activity-logs' => 'App\\Models\\ActivityLog',
        ];

        foreach ($modelMap as $key => $model) {
            if (str_contains($path, $key)) {
                return $model;
            }
        }

        return null;
    }

    /**
     * Extract model ID from path or request with enhanced detection
     */
    private function extractModelId(string $path, Request $request): ?string
    {
        // Try to extract ID from URL path patterns
        // Pattern 1: /api/resource/ID (e.g., /api/users/123, /api/congdap/C001)
        if (preg_match('/\/([A-Z0-9]+)(?:\/[^\/]*)?$/', $path, $matches)) {
            $potentialId = $matches[1];

            // Check if it's actually an ID (not a method name)
            if (!in_array(strtolower($potentialId), ['filters', 'export', 'import', 'stats'])) {
                return $potentialId;
            }
        }

        // Pattern 2: /api/resource/ID/action (e.g., /api/users/123/edit)
        if (preg_match('/\/([A-Z0-9]+)\/[a-z]+$/', $path, $matches)) {
            return $matches[1];
        }

        // Try to get ID from request data
        $idFields = ['id', 'user_id', 'role_id', 'permission_id'];
        foreach ($idFields as $field) {
            if ($request->has($field)) {
                return (string) $request->input($field);
            }
        }

        // For POST requests, try to get ID from response if available
        if ($request->method() === 'POST' && $request->has('name')) {
            // This will be filled after the model is created
            return null;
        }

        return null;
    }
}
