<?php

namespace App\Events;

use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class AssetUpdated
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $model;
    public $oldValues;
    public $newValues;
    public $user;
    public $ipAddress;
    public $userAgent;

    /**
     * Create a new event instance.
     */
    public function __construct(Model $model, array $oldValues, array $newValues, $user = null, ?string $ipAddress = null, ?string $userAgent = null)
    {
        $this->model = $model;
        $this->oldValues = $oldValues;
        $this->newValues = $newValues;
        $this->user = $user ?? auth()->user();
        $this->ipAddress = $ipAddress ?? request()->ip();
        $this->userAgent = $userAgent ?? request()->userAgent();
    }
}
