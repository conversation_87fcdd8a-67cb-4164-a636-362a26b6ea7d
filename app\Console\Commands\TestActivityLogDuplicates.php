<?php

namespace App\Console\Commands;

use App\Models\ActivityLog;
use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class TestActivityLogDuplicates extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'activity-log:test-duplicates
                            {--clear : Clear existing test logs before running}
                            {--scenarios=all : Which scenarios to test (all, auth, crud, api)}';

    /**
     * The console command description.
     */
    protected $description = 'Test activity logging system to verify "One Action = One Log" principle';

    /**
     * Test results storage
     */
    private array $testResults = [];
    private int $totalTests = 0;
    private int $passedTests = 0;
    private int $failedTests = 0;

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🧪 Testing Activity Log Duplicate Prevention System');
        $this->info('=================================================');

        if ($this->option('clear')) {
            $this->clearTestLogs();
        }

        $scenarios = $this->option('scenarios');

        switch ($scenarios) {
            case 'auth':
                $this->testAuthenticationScenarios();
                break;
            case 'crud':
                $this->testCrudScenarios();
                break;
            case 'api':
                $this->testApiScenarios();
                break;
            case 'all':
            default:
                $this->testAuthenticationScenarios();
                $this->testCrudScenarios();
                $this->testApiScenarios();
                break;
        }

        $this->displayResults();

        return $this->failedTests === 0 ? 0 : 1;
    }

    /**
     * Clear test logs
     */
    private function clearTestLogs(): void
    {
        $this->info('🧹 Clearing existing test logs...');

        ActivityLog::where('description', 'like', '%[TEST]%')->delete();

        $this->info('✅ Test logs cleared');
        $this->newLine();
    }

    /**
     * Test authentication scenarios
     */
    private function testAuthenticationScenarios(): void
    {
        $this->info('🔐 Testing Authentication Scenarios');
        $this->line('------------------------------------');

        // Test 1: Multiple login attempts should create separate logs
        $this->runTest('Multiple Login Attempts', function () {
            $user = User::first();
            if (!$user) {
                throw new \Exception('No users found for testing');
            }

            $initialCount = ActivityLog::count();

            // Simulate multiple login attempts (each is a separate action)
            for ($i = 0; $i < 3; $i++) {
                ActivityLog::createLog([
                    'user_id' => $user->id,
                    'action' => 'login',
                    'description' => '[TEST] User login attempt ' . ($i + 1),
                    'ip_address' => '127.0.0.1',
                    'user_agent' => 'Test Browser',
                ]);
            }

            $finalCount = ActivityLog::count();
            $logsCreated = $finalCount - $initialCount;

            // Should create 3 separate login logs (each is a separate action)
            return $logsCreated === 3;
        });

        // Test 2: Logout logging
        $this->runTest('Logout Logging', function () {
            $user = User::first();
            $initialCount = ActivityLog::count();

            ActivityLog::createLog([
                'user_id' => $user->id,
                'action' => 'logout',
                'description' => '[TEST] User logout test',
                'ip_address' => '127.0.0.1',
                'user_agent' => 'Test Browser',
            ]);

            $finalCount = ActivityLog::count();
            $logsCreated = $finalCount - $initialCount;

            return $logsCreated === 1;
        });
    }

    /**
     * Test CRUD scenarios
     */
    private function testCrudScenarios(): void
    {
        $this->info('📝 Testing CRUD Scenarios');
        $this->line('-------------------------');

        // Test 1: Create operation logging
        $this->runTest('Create Operation Logging', function () {
            $user = User::first();
            $initialCount = ActivityLog::count();

            ActivityLog::createLog([
                'user_id' => $user->id,
                'action' => 'create',
                'description' => '[TEST] Create congdap record',
                'model_type' => 'App\\Models\\API\\Taisan\\Congdap',
                'model_id' => 'C001',
                'new_values' => ['name' => 'Test Congdap', 'location' => 'Test Location'],
                'ip_address' => '127.0.0.1',
                'user_agent' => 'Test Browser',
            ]);

            $finalCount = ActivityLog::count();
            $logsCreated = $finalCount - $initialCount;

            return $logsCreated === 1;
        });

        // Test 2: Update operation with old/new values
        $this->runTest('Update Operation with Values', function () {
            $user = User::first();
            $initialCount = ActivityLog::count();

            ActivityLog::createLog([
                'user_id' => $user->id,
                'action' => 'update',
                'description' => '[TEST] Update congdap record',
                'model_type' => 'App\\Models\\API\\Taisan\\Congdap',
                'model_id' => 'C001',
                'old_values' => ['name' => 'Old Name', 'location' => 'Old Location'],
                'new_values' => ['name' => 'New Name', 'location' => 'New Location'],
                'ip_address' => '127.0.0.1',
                'user_agent' => 'Test Browser',
            ]);

            $finalCount = ActivityLog::count();
            $logsCreated = $finalCount - $initialCount;

            // Verify the log has both old and new values
            $log = ActivityLog::latest()->first();
            $hasOldValues = !empty($log->old_values);
            $hasNewValues = !empty($log->new_values);

            return $logsCreated === 1 && $hasOldValues && $hasNewValues;
        });

        // Test 3: Delete operation logging
        $this->runTest('Delete Operation Logging', function () {
            $user = User::first();
            $initialCount = ActivityLog::count();

            ActivityLog::createLog([
                'user_id' => $user->id,
                'action' => 'delete',
                'description' => '[TEST] Delete congdap record',
                'model_type' => 'App\\Models\\API\\Taisan\\Congdap',
                'model_id' => 'C001',
                'old_values' => ['name' => 'Deleted Item', 'location' => 'Deleted Location'],
                'ip_address' => '127.0.0.1',
                'user_agent' => 'Test Browser',
            ]);

            $finalCount = ActivityLog::count();
            $logsCreated = $finalCount - $initialCount;

            return $logsCreated === 1;
        });
    }

    /**
     * Test API scenarios
     */
    private function testApiScenarios(): void
    {
        $this->info('🌐 Testing API Scenarios');
        $this->line('------------------------');

        // Test 1: Import operation logging
        $this->runTest('Import Operation Logging', function () {
            $user = User::first();
            $initialCount = ActivityLog::count();

            ActivityLog::createLog([
                'user_id' => $user->id,
                'action' => 'import',
                'description' => '[TEST] Import Excel file with 100 records',
                'model_type' => 'App\\Models\\API\\Taisan\\Congdap',
                'new_values' => ['file_name' => 'test_import.xlsx', 'records_count' => 100],
                'ip_address' => '127.0.0.1',
                'user_agent' => 'Test Browser',
            ]);

            $finalCount = ActivityLog::count();
            $logsCreated = $finalCount - $initialCount;

            return $logsCreated === 1;
        });

        // Test 2: Export operation logging
        $this->runTest('Export Operation Logging', function () {
            $user = User::first();
            $initialCount = ActivityLog::count();

            ActivityLog::createLog([
                'user_id' => $user->id,
                'action' => 'export',
                'description' => '[TEST] Export congdap data to Excel',
                'model_type' => 'App\\Models\\API\\Taisan\\Congdap',
                'new_values' => ['export_type' => 'excel', 'records_count' => 50],
                'ip_address' => '127.0.0.1',
                'user_agent' => 'Test Browser',
            ]);

            $finalCount = ActivityLog::count();
            $logsCreated = $finalCount - $initialCount;

            return $logsCreated === 1;
        });

        // Test 3: View operations logging
        $this->runTest('View Operations Logging', function () {
            $user = User::first();
            $initialCount = ActivityLog::count();

            // Multiple view operations (each should be logged separately)
            for ($i = 0; $i < 3; $i++) {
                ActivityLog::createLog([
                    'user_id' => $user->id,
                    'action' => 'view',
                    'description' => '[TEST] View congdap list page ' . ($i + 1),
                    'model_type' => 'App\\Models\\API\\Taisan\\Congdap',
                    'ip_address' => '127.0.0.1',
                    'user_agent' => 'Test Browser',
                ]);
            }

            $finalCount = ActivityLog::count();
            $logsCreated = $finalCount - $initialCount;

            // Should create 3 separate view logs
            return $logsCreated === 3;
        });
    }

    /**
     * Run a single test
     */
    private function runTest(string $testName, callable $testFunction): void
    {
        $this->totalTests++;

        try {
            $result = $testFunction();

            if ($result) {
                $this->passedTests++;
                $this->testResults[] = ['name' => $testName, 'status' => 'PASS', 'message' => ''];
                $this->line("  ✅ {$testName}");
            } else {
                $this->failedTests++;
                $this->testResults[] = ['name' => $testName, 'status' => 'FAIL', 'message' => 'Test returned false'];
                $this->line("  ❌ {$testName}");
            }
        } catch (\Exception $e) {
            $this->failedTests++;
            $this->testResults[] = ['name' => $testName, 'status' => 'ERROR', 'message' => $e->getMessage()];
            $this->line("  💥 {$testName} - Error: " . $e->getMessage());
        }
    }

    /**
     * Display test results
     */
    private function displayResults(): void
    {
        $this->newLine();
        $this->info('📊 Test Results Summary');
        $this->info('======================');

        $this->table(
            ['Test Name', 'Status', 'Message'],
            array_map(function ($result) {
                return [
                    $result['name'],
                    $result['status'],
                    $result['message']
                ];
            }, $this->testResults)
        );

        $this->newLine();
        $this->info("Total Tests: {$this->totalTests}");
        $this->info("Passed: {$this->passedTests}");
        $this->info("Failed: {$this->failedTests}");

        if ($this->failedTests === 0) {
            $this->info('🎉 All tests passed! Activity logging system is working correctly.');
        } else {
            $this->error('❌ Some tests failed. Please review the activity logging system.');
        }

        // Show recent test logs
        $this->newLine();
        $this->info('📋 Recent Test Logs (last 10):');
        $recentLogs = ActivityLog::where('description', 'like', '%[TEST]%')
            ->latest()
            ->limit(10)
            ->get(['action', 'description', 'created_at']);

        if ($recentLogs->count() > 0) {
            $this->table(
                ['Action', 'Description', 'Created At'],
                $recentLogs->map(function ($log) {
                    return [
                        $log->action,
                        $log->description,
                        $log->created_at->format('Y-m-d H:i:s')
                    ];
                })->toArray()
            );
        } else {
            $this->line('No test logs found.');
        }

        // Check for potential duplicates
        $this->newLine();
        $this->info('🔍 Checking for Potential Duplicates:');
        $duplicates = DB::select("
            SELECT
                user_id,
                action,
                description,
                ip_address,
                COUNT(*) as count,
                GROUP_CONCAT(id) as log_ids
            FROM activity_logs
            WHERE description LIKE '%[TEST]%'
            GROUP BY user_id, action, description, ip_address
            HAVING COUNT(*) > 1
        ");

        if (count($duplicates) > 0) {
            $this->error('⚠️  Found potential duplicates:');
            foreach ($duplicates as $duplicate) {
                $this->line("  - User {$duplicate->user_id}, Action: {$duplicate->action}, Count: {$duplicate->count}");
                $this->line("    IDs: {$duplicate->log_ids}");
            }
        } else {
            $this->info('✅ No duplicates found in test logs.');
        }
    }
}
