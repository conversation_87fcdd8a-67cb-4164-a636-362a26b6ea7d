<?php

namespace App\Console\Commands;

use App\Events\ActivityLogged;
use App\Events\UserLoggedIn;
use App\Events\UserLoggedOut;
use App\Events\AssetCreated;
use App\Models\ActivityLog;
use App\Models\User;
use App\Models\API\Taisan\Congdap;
use App\Services\ActivityLogService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Event;

class TestActivityLog extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:activity-log';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test the activity logging system';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('=== Testing Activity Log System ===');
        $this->newLine();

        // Test 1: Check if ActivityLog model works
        $this->info('1. Testing ActivityLog model...');
        $logCount = ActivityLog::count();
        $this->info("✓ Current activity logs count: {$logCount}");
        
        // Test action name accessor
        $testLog = new ActivityLog(['action' => 'login']);
        $this->info("✓ Action name accessor: '{$testLog->action_name}'");
        $this->newLine();

        // Test 2: Test ActivityLogService
        $this->info('2. Testing ActivityLogService...');
        
        try {
            // Test logging a custom activity
            ActivityLogService::logActivity(
                'test_action',
                'Testing activity log system via command',
                'App\Models\User',
                '1'
            );
            $this->info('✓ Custom activity logged successfully');
            
            // Test import logging
            ActivityLogService::logImport('App\Models\API\Taisan\Congdap', 5, 2, ['Error 1', 'Error 2']);
            $this->info('✓ Import activity logged successfully');
            
            // Test export logging
            ActivityLogService::logExport('App\Models\API\Taisan\Congdap', 10, ['filter' => 'test']);
            $this->info('✓ Export activity logged successfully');
            
        } catch (\Exception $e) {
            $this->error('✗ Error in ActivityLogService: ' . $e->getMessage());
        }
        $this->newLine();

        // Test 3: Test Events
        $this->info('3. Testing Events...');
        
        try {
            // Create a test user if none exists
            $user = User::first();
            if (!$user) {
                $this->warn('No users found in database. Creating a test user...');
                $user = User::create([
                    'name' => 'Test User',
                    'email' => '<EMAIL>',
                    'password' => bcrypt('password'),
                ]);
                $this->info('✓ Test user created');
            }

            // Test UserLoggedIn event
            event(new UserLoggedIn($user, '127.0.0.1', 'Test Browser'));
            $this->info('✓ UserLoggedIn event fired');

            // Test UserLoggedOut event
            event(new UserLoggedOut($user, '127.0.0.1', 'Test Browser'));
            $this->info('✓ UserLoggedOut event fired');

            // Test ActivityLogged event directly
            event(new ActivityLogged(
                $user->id,
                'test_direct',
                'Direct activity log test',
                'App\Models\User',
                $user->id
            ));
            $this->info('✓ ActivityLogged event fired directly');

        } catch (\Exception $e) {
            $this->error('✗ Error in Events: ' . $e->getMessage());
        }
        $this->newLine();

        // Test 4: Check database structure
        $this->info('4. Checking database structure...');
        
        try {
            $columns = \DB::select("SELECT column_name FROM information_schema.columns WHERE table_name = 'activity_logs'");
            if (!empty($columns)) {
                $this->info('✓ Activity logs table exists with columns:');
                foreach ($columns as $column) {
                    $this->line("  - {$column->column_name}");
                }
            } else {
                $this->error('✗ Activity logs table not found');
            }
        } catch (\Exception $e) {
            $this->error('✗ Error checking database: ' . $e->getMessage());
        }
        $this->newLine();

        // Test 5: Check API routes
        $this->info('5. Checking API routes...');
        
        $routes = app('router')->getRoutes();
        $activityLogRoutes = [];
        
        foreach ($routes as $route) {
            if (strpos($route->uri(), 'activity-logs') !== false) {
                $activityLogRoutes[] = $route->uri();
            }
        }
        
        if (!empty($activityLogRoutes)) {
            $this->info('✓ Activity log routes are registered:');
            foreach ($activityLogRoutes as $route) {
                $this->line("  - {$route}");
            }
        } else {
            $this->error('✗ No activity log routes found');
        }
        $this->newLine();

        // Test 6: Final summary
        $this->info('6. Final summary...');
        $finalLogCount = ActivityLog::count();
        $newLogs = $finalLogCount - $logCount;
        $this->info("✓ New activity logs created during test: {$newLogs}");
        
        if ($newLogs > 0) {
            $this->info('✓ Activity logging system is working!');
            
            // Show latest logs
            $latestLogs = ActivityLog::latest()->take(5)->get();
            $this->info("\nLatest activity logs:");
            foreach ($latestLogs as $log) {
                $this->line("  - [{$log->created_at}] {$log->action}: {$log->description}");
            }
        } else {
            $this->warn('⚠ No new logs were created - check event listeners');
        }

        $this->newLine();
        $this->info('=== Test Complete ===');
        
        return 0;
    }
}
