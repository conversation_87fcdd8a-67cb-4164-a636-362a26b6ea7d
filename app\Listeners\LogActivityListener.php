<?php

namespace App\Listeners;

use App\Events\ActivityLogged;
use App\Models\ActivityLog;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Cache;

class LogActivityListener implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * Handle the event.
     */
    public function handle(ActivityLogged $event): void
    {
        try {
            ActivityLog::create([
                'user_id' => $event->userId,
                'action' => $event->action,
                'description' => $event->description,
                'model_type' => $event->modelType,
                'model_id' => $event->modelId,
                'old_values' => $event->oldValues,
                'new_values' => $event->newValues,
                'ip_address' => $event->ipAddress,
                'user_agent' => $event->userAgent,
            ]);

            // Invalidate activity logs cache
            $this->invalidateCache();
        } catch (\Exception $e) {
            // Log error but don't break the application
            \Log::error('Failed to create activity log: ' . $e->getMessage());
        }
    }

    /**
     * Invalidate activity logs cache
     */
    private function invalidateCache(): void
    {
        try {
            // Try cache tags first
            Cache::tags(['activity-logs-list'])->flush();
        } catch (\Exception $e) {
            // Fallback for drivers that don't support tags
            $keys = [
                'activity_logs_page_*',
                'activity_logs_users',
                'activity_logs_actions',
            ];

            foreach ($keys as $pattern) {
                if (strpos($pattern, '*') !== false) {
                    // For wildcard patterns, we need to implement a custom solution
                    // This is a simplified approach - in production you might want to use Redis SCAN
                    for ($i = 1; $i <= 100; $i++) {
                        Cache::forget(str_replace('*', $i, $pattern));
                    }
                } else {
                    Cache::forget($pattern);
                }
            }
        }
    }
}
