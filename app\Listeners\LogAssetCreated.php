<?php

namespace App\Listeners;

use App\Events\ActivityLogged;
use App\Events\AssetCreated;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class LogAssetCreated implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * Handle the event.
     */
    public function handle(AssetCreated $event): void
    {
        $modelClass = get_class($event->model);
        $modelName = $this->getModelDisplayName($modelClass);
        $assetName = $event->model->ten ?? $event->model->name ?? $event->model->id;
        
        $userName = $event->user ? $event->user->name : '<PERSON>ệ thống';
        $description = "Người dùng {$userName} đã tạo mới {$modelName}: {$assetName}";

        event(new ActivityLogged(
            $event->user?->id,
            'create',
            $description,
            $modelClass,
            $event->model->id,
            null,
            $event->model->toArray(),
            $event->ipAddress,
            $event->userAgent
        ));
    }

    /**
     * Get display name for model
     */
    private function getModelDisplayName(string $modelClass): string
    {
        $modelMap = [
            'App\Models\API\Taisan\Congdap' => 'Cống đập',
            'App\Models\API\Taisan\Trambom' => 'Trạm bơm',
            'App\Models\API\Taisan\Kenhmuong' => 'Kênh mương',
            'App\Models\User' => 'Người dùng',
            'Spatie\Permission\Models\Role' => 'Vai trò',
            'Spatie\Permission\Models\Permission' => 'Quyền',
        ];

        return $modelMap[$modelClass] ?? class_basename($modelClass);
    }
}
