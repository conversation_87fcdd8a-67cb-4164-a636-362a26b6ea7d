<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import axios from 'axios'
import { Button } from '@/components/ui/button'
import Icon from '@/components/Icon.vue'

interface Permission {
  id: string | number
  name: string
  vi_name?: string
  group?: string
}
interface Role {
  id: string | number
  name: string
  vi_name?: string
  description?: string | null
  is_custom?: boolean
  permissions?: Permission[]
}

const roles = ref<Role[]>([])
const activeRole = ref<Role | null>(null)
const loading = ref(false)
const error = ref<string | null>(null)
const allPermissions = ref<Permission[]>([])
const selectedPermissionIds = ref<(string|number)[]>([])
const saving = ref(false)

const groupNameMapping: Record<string, string> = {
  data: 'Quản lý Dữ liệu',
  report: 'Báo cáo & Thống kê',
  user: '<PERSON><PERSON><PERSON><PERSON> lý Ng<PERSON>ờ<PERSON> dùng',
  system: '<PERSON><PERSON> thống',
  calculation: '<PERSON><PERSON><PERSON> năng <PERSON> toán',
  profile: '<PERSON><PERSON> sơ cá nhân',
  general: 'Chung'
}

const groupedPermissions = computed(() => {
  if (!allPermissions.value.length) return {};

  const groupOrder = ['data', 'report', 'user', 'calculation', 'system', 'profile'];
  const groups = allPermissions.value.reduce((acc, perm) => {
    const groupKey = perm.group || 'general';
    if (!acc[groupKey]) {
      acc[groupKey] = [];
    }
    acc[groupKey].push(perm);
    return acc;
  }, {} as Record<string, Permission[]>);

  return Object.keys(groups)
    .sort((a, b) => {
      const indexA = groupOrder.indexOf(a);
      const indexB = groupOrder.indexOf(b);
      if (indexA === -1 && indexB === -1) return a.localeCompare(b);
      if (indexA === -1) return 1;
      if (indexB === -1) return -1;
      return indexA - indexB;
    })
    .reduce((acc, key) => {
      acc[key] = groups[key];
      return acc;
    }, {} as Record<string, Permission[]>);
});

const fetchPermissions = async () => {
  try {
    const res = await axios.get('/api/permissions')
    allPermissions.value = res.data.data
  } catch (e: any) {
    error.value = e.message || 'Lỗi tải danh sách quyền hạn'
  }
}

const fetchRoles = async () => {
  loading.value = true
  error.value = null
  try {
    const res = await axios.get('/api/roles')
    roles.value = res.data.data
    if (roles.value.length > 0) {
      await fetchRoleDetail(roles.value[0].id)
    } else {
      activeRole.value = null
      selectedPermissionIds.value = []
    }
  } catch (e: any) {
    error.value = e.message || 'Lỗi tải dữ liệu vai trò'
  } finally {
    loading.value = false
  }
}

const fetchRoleDetail = async (roleId: string | number) => {
  loading.value = true
  error.value = null
  try {
    const res = await axios.get(`/api/roles/${roleId}`)
    activeRole.value = res.data.data
    selectedPermissionIds.value = (activeRole.value?.permissions || []).map(p => p.id)
  } catch (e: any) {
    error.value = e.message || 'Lỗi tải chi tiết vai trò'
    activeRole.value = null
    selectedPermissionIds.value = []
  } finally {
    loading.value = false
  }
}

const isPermissionChecked = (permId: string|number) => {
  return selectedPermissionIds.value.includes(permId)
}

const isAnyPermissionInGroupChecked = (permissionsInGroup: Permission[]): boolean => {
  if (!permissionsInGroup || permissionsInGroup.length === 0) {
    return false;
  }
  return permissionsInGroup.some(perm => isPermissionChecked(perm.id));
}

const togglePermission = (permId: string|number) => {
  if (isPermissionChecked(permId)) {
    selectedPermissionIds.value = selectedPermissionIds.value.filter(id => id !== permId)
  } else {
    selectedPermissionIds.value = [...selectedPermissionIds.value, permId]
  }
}

const savePermissions = async () => {
  if (!activeRole.value) return
  saving.value = true
  error.value = null
  try {
    await axios.put(`/api/roles/${activeRole.value.id}/permissions`, {
      permissions: selectedPermissionIds.value
    })
    await fetchRoleDetail(activeRole.value.id)
  } catch (e: any) {
    error.value = e.message || 'Lỗi cập nhật quyền hạn'
  } finally {
    saving.value = false
  }
}

onMounted(async () => {
  await fetchPermissions()
  await fetchRoles()
})
</script>

<template>
  <div>
    <div class="flex justify-between items-center">
      <h2 class="text-xl font-semibold">
        Quản Lý Phân Quyền
      </h2>
      <Button>
        <Icon name="Plus" class="h-4 w-4 mr-2" />
        Thêm vai trò
      </Button>
    </div>

    <div v-if="loading" class="text-center py-8">Đang tải...</div>
    <div v-else-if="error" class="text-center text-red-500 py-8">{{ error }}</div>
    <div v-else-if="!roles.length" class="text-center py-8">Không có vai trò nào</div>
    <div v-else>
      <div class="flex items-center gap-2 mb-4">
        <Button
          v-for="role in roles"
          :key="role.id"
          :variant="activeRole && activeRole.id === role.id ? 'default' : 'outline'"
          @click="fetchRoleDetail(role.id)"
        >
          {{ role.vi_name || role.name }}
        </Button>
      </div>

      <div class="border rounded-md overflow-y-auto" style="max-height: 70vh;">
        <div v-for="(perms, groupName) in groupedPermissions" :key="String(groupName)">
          <details class="border-b last:border-b-0" :open="isAnyPermissionInGroupChecked(perms)">
            <summary class="p-3 bg-gray-100 hover:bg-gray-200 cursor-pointer font-semibold text-sm capitalize flex justify-between items-center">
              <span>{{ groupNameMapping[String(groupName)] || groupName }}</span>
              <Icon name="ChevronDown" class="h-4 w-4 transition-transform duration-200 icon-chevron" />
            </summary>
            <table class="w-full">
              <tbody>
                <tr v-for="perm in perms" :key="perm.id" class="hover:bg-gray-100">
                  <td class="px-3 py-px font-normal w-2/3">
                    {{ perm.vi_name || perm.name }}
                  </td>
                  <td class="px-3 py-px text-right w-1/3">
                    <input
                      type="checkbox"
                      :checked="isPermissionChecked(perm.id)"
                      @change="togglePermission(perm.id)"
                      class="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                      :aria-label="perm.vi_name || perm.name"
                    >
                  </td>
                </tr>
              </tbody>
            </table>
          </details>
        </div>
      </div>
      <div class="flex justify-end mt-4">
        <Button :disabled="saving" @click="savePermissions">
          <Icon name="Save" class="h-4 w-4 mr-2" />
          <span v-if="saving">Đang lưu...</span>
          <span v-else>Lưu thay đổi</span>
        </Button>
      </div>
    </div>
  </div>
</template>

<style scoped>
details[open] > summary .icon-chevron {
  transform: rotate(180deg);
}
</style>
