<?php

namespace App\Http\Controllers\API\Taisan;

use App\Http\Controllers\Controller;
use App\Http\Requests\Taisan\StoreCongdapRequest;
use App\Http\Requests\Taisan\StoreCongdapImportRequest;
use App\Http\Requests\Taisan\UpdateCongdapRequest;
use App\Http\Resources\Taisan\CongdapResource;
use App\Http\Resources\Taisan\CongdapGeometryResource;
use App\Http\Resources\Taisan\CongdapAttributesResource;
use App\Http\Resources\Taisan\CongdapImportResource;
use App\Services\CongdapService;
use App\Services\CongdapImportService;
use App\Services\ActivityLogService;
use App\Traits\ApiResponseTrait;
use App\Exceptions\Taisan\CongdapNotFoundException;
use App\Exceptions\Taisan\InvalidGeometryException;
use App\Exceptions\Taisan\InvalidExcelFormatException;
use App\Exceptions\Taisan\ImportValidationException;
use App\Exceptions\Taisan\BatchProcessingException;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Throwable;
use Illuminate\Support\Facades\Gate;

class CongdapController extends Controller
{
    use ApiResponseTrait;

    protected $congdapService;
    protected $congdapImportService;

    public function __construct(CongdapService $congdapService, CongdapImportService $congdapImportService)
    {
        $this->congdapService = $congdapService;
        $this->congdapImportService = $congdapImportService;
        // --- Áp dụng Middleware để kiểm tra quyền CHUNG ---
        // Cách này hiệu quả cho việc kiểm tra "User có quyền thực hiện hành động này nói chung không?"

        /* // Quyền xem (index, show, geometry, attributes)
        $this->middleware('permission:view_data')->only(['index', 'show', 'geometry', 'attributes']);

        // Quyền tạo (store)
        $this->middleware('permission:create_data')->only(['store']);

        // Quyền sửa (update)
        $this->middleware('permission:edit_data')->only(['update']);

        // Quyền xóa (destroy)
        $this->middleware('permission:delete_data')->only(['destroy']);
 */
        /*
        // --- Chuẩn bị cho Bước 2: Sử dụng Policies (Khi cần kiểm tra trên đối tượng cụ thể) ---
        // Khi bạn cần kiểm tra quyền trên từng Congdap cụ thể, bạn sẽ:
        // 1. Tạo một Policy: `php artisan make:policy CongdapPolicy --model=Taisan\\Congdap`
        // 2. Đăng ký Policy trong `AuthServiceProvider`.
        // 3. Thay thế hoặc bổ sung middleware bằng cách gọi `$this->authorize()` trong các phương thức controller.

        // Ví dụ (sẽ dùng sau này khi có Policy):
        // $this->authorizeResource(Congdap::class, 'congdap'); // Áp dụng policy cho các action chuẩn
        // Hoặc gọi trong từng phương thức:
        // public function show(Congdap $congdap) { // Sử dụng Route Model Binding
        //     $this->authorize('view', $congdap); // Kiểm tra quyền 'view' trên $congdap cụ thể
        //     // ...
        // }
        // public function update(UpdateCongdapRequest $request, Congdap $congdap) {
        //     $this->authorize('update', $congdap); // Kiểm tra quyền 'update' trên $congdap cụ thể
        //     // ...
        // }
        */
    }

    public function index()
    {
        try {
            $filters = [
                'id_xa' => request('id_xa'),
                'loai_ct' => request('loai_ct'),
                'search' => request('search')
            ];

            $perPage = request('per_page', 10);

            $paginator = $this->congdapService->getList($filters, $perPage);

            return $this->paginatedGeoJsonResponse(
                paginator: $paginator,
                resource: CongdapResource::class,
                message: 'Lấy danh sách cống đập thành công'
            );

        } catch (Throwable $e) {
            Log::error('Error fetching Congdap list: ' . $e->getMessage());
            return $this->errorResponse('Lỗi khi lấy danh sách cống đập', 500);
        }
    }

    public function store(StoreCongdapRequest $request)
    {
        try {
            $congdap = $this->congdapService->create($request->validated());

            // Log activity
            ActivityLogService::logAssetCreated($congdap);

            // Xóa cache có chủ đích sau khi tạo thành công
            $this->clearCongdapCache();

            return $this->createdResponse(
                data: new CongdapResource($congdap),
                message: 'Tạo cống đập thành công'
            );

        } catch (InvalidGeometryException $e) {
            return $this->errorResponse($e->getMessage(), 422);
        } catch (Throwable $e) {
            Log::error('Error creating Congdap: ' . $e->getMessage());
            return $this->errorResponse('Lỗi khi tạo cống đập', 500);
        }
    }

    public function show(string $id)
    {
        try {
            $congdap = $this->congdapService->getById($id);

            return $this->successResponse(
                data: new CongdapResource($congdap),
                message: 'Lấy thông tin cống đập thành công'
            );

        } catch (CongdapNotFoundException $e) {
            return $this->errorResponse($e->getMessage(), 404);
        } catch (Throwable $e) {
            Log::error('Error fetching Congdap ID ' . $id . ': ' . $e->getMessage());
            return $this->errorResponse('Lỗi khi lấy thông tin cống đập', 500);
        }
    }

    public function update(UpdateCongdapRequest $request, string $id)
    {
        try {
            // Get old values before update
            $oldCongdap = $this->congdapService->getById($id);
            $oldValues = $oldCongdap->toArray();

            $congdap = $this->congdapService->update($id, $request->validated());
            $newValues = $request->validated();

            // Log activity
            ActivityLogService::logAssetUpdated($congdap, $oldValues, $newValues);

            // Xóa cache có chủ đích sau khi cập nhật thành công
            $this->clearCongdapCache();

            return $this->updatedResponse(
                data: new CongdapResource($congdap),
                message: 'Cập nhật cống đập thành công'
            );

        } catch (CongdapNotFoundException $e) {
            return $this->errorResponse($e->getMessage(), 404);
        } catch (InvalidGeometryException $e) {
            return $this->errorResponse($e->getMessage(), 422);
        } catch (Throwable $e) {
            Log::error('Error updating Congdap ID ' . $id . ': ' . $e->getMessage());
            return $this->errorResponse('Lỗi khi cập nhật cống đập', 500);
        }
    }

    public function destroy(string $id)
    {
        try {
            // Get old values before deletion
            $congdap = $this->congdapService->getById($id);
            $oldValues = $congdap->toArray();

            $this->congdapService->delete($id);

            // Log activity
            ActivityLogService::logAssetDeleted($congdap, $oldValues);

            // Xóa cache có chủ đích sau khi xóa thành công
            $this->clearCongdapCache();

            return $this->deletedResponse('Xóa cống đập thành công');

        } catch (CongdapNotFoundException $e) {
            return $this->errorResponse($e->getMessage(), 404);
        } catch (Throwable $e) {
            Log::error('Error deleting Congdap ID ' . $id . ': ' . $e->getMessage());
            return $this->errorResponse('Lỗi khi xóa cống đập', 500);
        }
    }

    /**
     * Lấy danh sách chỉ bao gồm geometry (không có thuộc tính)
     * Tối ưu tài nguyên khi chỉ cần hiển thị dữ liệu không gian
     */
    public function geometry()
    {
        try {
            $filters = [
                'id_xa' => request('id_xa'),
                'loai_ct' => request('loai_ct'),
                'search' => request('search')
            ];

            $paginator = $this->congdapService->getGeometryList($filters);

            return response()->json([
                'success' => true,
                'message' => 'Lấy dữ liệu geometry cống đập thành công',
                'data' => [
                    'type' => 'FeatureCollection',
                    'features' => CongdapGeometryResource::collection($paginator->items())
                ]
            ]);

        } catch (Throwable $e) {
            Log::error('Error fetching Congdap geometry: ' . $e->getMessage());
            return $this->errorResponse('Lỗi khi lấy dữ liệu geometry cống đập', 500);
        }
    }

    /**
     * Lấy danh sách chỉ bao gồm thuộc tính (không có geometry)
     * Tối ưu tài nguyên khi chỉ cần hiển thị thông tin thuộc tính
     */
    public function attributes()
    {
        try {
            $filters = [
                'id_xa' => request('id_xa'),
                'loai_ct' => request('loai_ct'),
                'search' => request('search')
            ];

            $perPage = request('per_page', 10);

            $paginator = $this->congdapService->getAttributesList($filters, $perPage);

            return $this->paginatedResponse(
                paginator: $paginator,
                resource: CongdapAttributesResource::class,
                message: 'Lấy dữ liệu thuộc tính cống đập thành công'
            );

        } catch (Throwable $e) {
            Log::error('Error fetching Congdap attributes: ' . $e->getMessage());
            return $this->errorResponse('Lỗi khi lấy dữ liệu thuộc tính cống đập', 500);
        }
    }

    /**
     * Import Excel data into congdap table
     */
    public function import(StoreCongdapImportRequest $request)
    {
        try {
            $file = $request->file('file');
            $options = [
                'batch_size' => $request->input('batch_size', 100),
                'skip_errors' => $request->input('skip_errors', false),
            ];

            // Perform the import
            $importResult = $this->congdapImportService->import($file, $options);

            // Log import activity
            ActivityLogService::logImport(
                'App\Models\API\Taisan\Congdap',
                $importResult['successful_imports'],
                $importResult['failed_imports'],
                $importResult['errors'] ?? []
            );

            // Clear cache after successful import
            if ($importResult['successful_imports'] > 0) {
                $this->clearCongdapCache();
            }

            // Return formatted response using resource
            return response()->json(
                (new CongdapImportResource($importResult))->toArray($request),
                ($importResult['successful_imports'] > 0 && $importResult['failed_imports'] === 0) ? 200 : 207 // 200 cho thành công hoàn toàn, 207 cho thành công một phần hoặc thất bại
            );

        } catch (InvalidExcelFormatException $e) {
            Log::error('Excel format error during import: ' . $e->getMessage());
            return $e->render();
        } catch (ImportValidationException $e) {
            Log::error('Validation error during import: ' . $e->getMessage());
            return $e->render();
        } catch (BatchProcessingException $e) {
            Log::error('Batch processing error during import: ' . $e->getMessage());
            return $e->render();
        } catch (Throwable $e) {
            Log::error('Unexpected error during Excel import: ' . $e->getMessage(), [
                'file' => $file->getClientOriginalName() ?? 'unknown',
                'exception' => $e
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Có lỗi không mong muốn xảy ra trong quá trình import. Vui lòng thử lại.',
                'code' => 'IMPORT_UNEXPECTED_ERROR',
                'data' => [
                    'errors' => [
                        [
                            'row' => 0,
                            'error' => 'Lỗi hệ thống: ' . $e->getMessage()
                        ]
                    ]
                ]
            ], 500);
        }
    }

    /**
     * Xóa cache có chủ đích cho dữ liệu cống đập
     */
    protected function clearCongdapCache(): void
    {
        $cacheTag = 'congdap-list';

        // Kiểm tra xem cache driver có hỗ trợ tags không
        $driver = config('cache.default');
        $supportedDrivers = ['redis', 'memcached', 'array'];

        if (in_array($driver, $supportedDrivers)) {
            Cache::tags([$cacheTag])->flush();
        } else {
            // Fallback: tăng cache version để invalidate tất cả cache
            $versionKey = 'congdap_version';
            $currentVersion = Cache::get($versionKey, 0);
            Cache::put($versionKey, $currentVersion + 1, 7200); // 2 hours
        }
    }
}
