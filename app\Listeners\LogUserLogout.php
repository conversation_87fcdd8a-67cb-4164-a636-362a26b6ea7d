<?php

namespace App\Listeners;

use App\Events\UserLoggedOut;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use App\Models\ActivityLog;

class LogUserLogout implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * Handle the event.
     */
    public function handle(UserLoggedOut $event): void
    {
        $description = "Người dùng {$event->user->name} ({$event->user->email}) đã đăng xuất khỏi hệ thống";

        ActivityLog::createLog([
            'user_id' => $event->user->id,
            'action' => 'logout',
            'description' => $description,
            'model_type' => 'App\\Models\\User',
            'model_id' => $event->user->id,
            'ip_address' => $event->ipAddress,
            'user_agent' => $event->userAgent,
        ]);
    }
}
