<?php

namespace App\Listeners;

use App\Events\ActivityLogged;
use App\Events\UserLoggedOut;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class LogUserLogout implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * Handle the event.
     */
    public function handle(UserLoggedOut $event): void
    {
        $description = "Người dùng {$event->user->name} ({$event->user->email}) đã đăng xuất khỏi hệ thống";

        event(new ActivityLogged(
            $event->user->id,
            'logout',
            $description,
            'App\Models\User',
            $event->user->id,
            null,
            null,
            $event->ipAddress,
            $event->userAgent
        ));
    }
}
