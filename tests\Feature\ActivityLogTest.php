<?php

namespace Tests\Feature;

use App\Events\ActivityLogged;
use App\Events\AssetCreated;
use App\Events\UserLoggedIn;
use App\Events\UserLoggedOut;
use App\Models\ActivityLog;
use App\Models\API\Taisan\Congdap;
use App\Models\User;
use App\Services\ActivityLogService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Event;
use Tests\TestCase;

class ActivityLogTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create a test user
        $this->user = User::factory()->create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
        ]);
    }

    /** @test */
    public function it_can_log_user_login()
    {
        Event::fake();

        // Simulate login event
        event(new UserLoggedIn($this->user, '127.0.0.1', 'Test Browser'));

        Event::assertDispatched(UserLoggedIn::class);
    }

    /** @test */
    public function it_can_log_user_logout()
    {
        Event::fake();

        // Simulate logout event
        event(new UserLoggedOut($this->user, '127.0.0.1', 'Test Browser'));

        Event::assertDispatched(UserLoggedOut::class);
    }

    /** @test */
    public function it_can_log_asset_creation()
    {
        Event::fake();

        // Create a mock Congdap model
        $congdap = new Congdap([
            'id' => 'C001',
            'ten' => 'Test Congdap',
            'quymo_ct' => 'Lớn',
        ]);

        // Simulate asset creation event
        event(new AssetCreated($congdap, $this->user));

        Event::assertDispatched(AssetCreated::class);
    }

    /** @test */
    public function it_can_create_activity_log_directly()
    {
        $this->actingAs($this->user);

        ActivityLogService::logActivity(
            'test_action',
            'Test activity description',
            'App\Models\User',
            $this->user->id
        );

        $this->assertDatabaseHas('activity_logs', [
            'user_id' => $this->user->id,
            'action' => 'test_action',
            'description' => 'Test activity description',
            'model_type' => 'App\Models\User',
            'model_id' => $this->user->id,
        ]);
    }

    /** @test */
    public function it_can_fetch_activity_logs_via_api()
    {
        $this->actingAs($this->user);

        // Create some test activity logs
        ActivityLog::create([
            'user_id' => $this->user->id,
            'action' => 'login',
            'description' => 'User logged in',
            'ip_address' => '127.0.0.1',
            'user_agent' => 'Test Browser',
        ]);

        $response = $this->getJson('/api/activity-logs');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'message',
                    'data' => [
                        '*' => [
                            'id',
                            'time',
                            'user',
                            'action',
                            'description',
                            'ip',
                            'device',
                        ]
                    ],
                    'meta'
                ]);
    }

    /** @test */
    public function it_can_filter_activity_logs()
    {
        $this->actingAs($this->user);

        // Create test logs with different actions
        ActivityLog::create([
            'user_id' => $this->user->id,
            'action' => 'login',
            'description' => 'User logged in',
        ]);

        ActivityLog::create([
            'user_id' => $this->user->id,
            'action' => 'create',
            'description' => 'User created something',
        ]);

        // Test filtering by action
        $response = $this->getJson('/api/activity-logs?action=login');

        $response->assertStatus(200);
        $data = $response->json('data');
        
        $this->assertCount(1, $data);
        $this->assertEquals('login', $data[0]['action_raw']);
    }

    /** @test */
    public function it_can_get_users_for_filtering()
    {
        $this->actingAs($this->user);

        // Create an activity log for the user
        ActivityLog::create([
            'user_id' => $this->user->id,
            'action' => 'test',
            'description' => 'Test action',
        ]);

        $response = $this->getJson('/api/activity-logs/filters/users');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'message',
                    'data' => [
                        '*' => [
                            'id',
                            'name',
                            'email',
                        ]
                    ]
                ]);
    }

    /** @test */
    public function it_can_get_actions_for_filtering()
    {
        $this->actingAs($this->user);

        // Create activity logs with different actions
        ActivityLog::create([
            'user_id' => $this->user->id,
            'action' => 'login',
            'description' => 'User logged in',
        ]);

        ActivityLog::create([
            'user_id' => $this->user->id,
            'action' => 'create',
            'description' => 'User created something',
        ]);

        $response = $this->getJson('/api/activity-logs/filters/actions');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'message',
                    'data' => [
                        '*' => [
                            'value',
                            'label',
                        ]
                    ]
                ]);
    }
}
